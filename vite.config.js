import vue from "@vitejs/plugin-vue";
import Components from "unplugin-vue-components/vite";
import { fileURLToPath, URL } from "node:url";
import fs from "fs-extra";
import path from "path";
import { defineConfig, loadEnv } from "vite";

export default defineConfig((mode) => {
  console.log(mode)
  const env = loadEnv(mode.mode, process.cwd());
  const less = env.VITE_APP_COMMON_LESS || "common.less";
  return {
    css: {
      preprocessorOptions: {
        less: {
          additionalData: `@import "@/styles/${less}";`, // 引入全局变量
        },
      },
    },
    plugins: [
      vue(),
      Components({}),
      {
        name: "generate-config-js",
        apply: "build", // 仅在构建时运行
        writeBundle() {
          // 获取打包输出目录
          const outputDir = path.resolve(__dirname, "fenqiH5Dist");

          // 生成 config.js 内容
          const configJsContent = `window.api='${env.VITE_APP_BASE_API}'`;

          // 确保打包目录存在
          fs.ensureDirSync(outputDir);

          // 将 config.js 写入到输出目录的根目录下
          fs.writeFileSync(path.join(outputDir, "config.js"), configJsContent);
        },
      },
    ],
    base: env.VITE_APP_BUILD_BASE,
    server: {
      host: "0.0.0.0",
    },
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    build: {
      outDir: "fenqiH5Dist",
      minify: "terser",
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
  };
});
