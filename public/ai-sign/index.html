<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>电子合同</title>

  <style>
    div {
      display: flex;
      flex-direction: column;
      padding: 10vh 10vw;
      align-items: center;
    }

    img {
      width: 80px;
      height: 80px;
    }

    span {
      margin: 30px auto 40px;
      font-weight: bold;
    }

    button {
      background-color: #6163e7;
      height: 48px;
      width: 100%;
      border: none;
      border-radius: 7px;
      color: #fff;
      font-size: 15px;
      cursor: pointer;
    }
  </style>
  <script src="../config.js"></script>
  <script src="./jquery.js"></script>
  <script>

    var code = getLocationSearch().code
    var url = getLocationSearch().url

    window.onload = function () {
      fetchContractStatus()
    }

    function fetchContractStatus() {
      $.ajax({
        type: 'GET',
        url: api + `/order/auth/query-contract-status/${code}`,
        contentType: "application/json",
        dataType: 'json',
        headers: {
          Accept: "*/*",
        },
        success: function (res) {
          if (res.code == 0) {
            const contractStatus = res.data.contractStatus
            console.log('返回----', contractStatus)

            if (contractStatus == 0) {
              $('#statusText').html('正在签署中...')
              $('#statusIcon').attr('src', './imgs/loading.gif')
              $('#statusIcon').css('width', '80px')
              $('#statusIcon').css('height', '80px')
              setTimeout(() => fetchContractStatus(), 2000)

            } else if (contractStatus == 1) {
              console.log('成功')
              $('#statusText').html('恭喜您，电子合同签署成功!')
              $('#statusIcon').attr('src', './imgs/success.png')
              $('#statusIcon').css('width', '160px')
              $('#statusIcon').css('height', '160px')
            } else if (contractStatus == 2) {
              $('#statusText').html(res.data.errorMsg || '签署失败！ ')
              $('#statusIcon').attr('src', './imgs/failed.png')
              $('#statusIcon').css('width', '100px')
              $('#statusIcon').css('height', '100px')
            }
          }
        },
        error: function (error) {
        }
      })
    }

    function jsJumpAppHome() {
      if (url) {
        window.location.replace(decodeURIComponent(url))
      } else {
        if (window.android) {
          if (window.android.jsJumpAppHome) {
            window.android.jsJumpAppHome();
          }
        } else {
          setupWebViewJavascriptBridge(function (bridge) {
            bridge.callHandler('jsJumpAppHome');
          });
        }
      }
    }

    function setupWebViewJavascriptBridge(callback) {
      if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
      if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
      window.WVJBCallbacks = [callback];
      var WVJBIframe = document.createElement('iframe');
      WVJBIframe.style.display = 'none';
      WVJBIframe.src = 'wvjbscheme://__BRIDGE_LOADED__';
      document.documentElement.appendChild(WVJBIframe);
      setTimeout(function () { document.documentElement.removeChild(WVJBIframe) }, 10);
    }

    function getLocationSearch(href = location.href) {
      const theRequest = new Object();
      if (href.indexOf("?") > -1) {
        const hrefStr = href.substr(href.indexOf("?") + 1);
        const arr = hrefStr.split("&");
        arr.filter(item => {
          theRequest[item.split('=')[0]] = item.split('=')[1];
        })
        return theRequest;
      }
      return {};
    }

    // (() => {
    //   function block() {
    //     setInterval(() => {
    //       debugger;
    //     }, 50);
    //   }
    //   try {
    //     block();
    //   } catch (err) { }
    // })();

  </script>
</head>

<body>
  <div>
    <img id="statusIcon" src="./imgs/loading.gif" />
    <span id="statusText">正在签署中...</span>
    <button onclick="jsJumpAppHome()">确定</button>
  </div>
</body>

</html>