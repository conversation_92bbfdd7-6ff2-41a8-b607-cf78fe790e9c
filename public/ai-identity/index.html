<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>身份认证</title>
  <style>
    div {
      display: flex;
      flex-direction: column;
      padding: 12vh 10vw;
      align-items: center;
    }

    img {
      width: 150px;
      height: 150px;
    }

    span {
      margin: 40px auto 40px;
      font-weight: bold;
    }

    button {
      background: linear-gradient(to right, #E9C599, #df9644);
      height: 48px;
      width: 100%;
      border: none;
      border-radius: 7px;
      color: #5C4A20;
      font-size: 15px;
      cursor: pointer;
    }
  </style>
  <script src="./jquery.js" ></script>
  <script>
    let { pass, msg,  url } = getLocationSearch();
    window.onload = function () {
      console.log(<PERSON><PERSON>an(pass), msg, url)
      if(pass == 'true'){
        $('#statusIcon').attr('src', './imgs/success.png')
        $('#statusText').html('认证流程已结束!')
        $('#actionButton').html('已完成，下一步')
      }else{
        $('#statusIcon').attr('src', './imgs/failed.png')
        $('#statusText').html(decodeURIComponent(msg))
        $('#statusText').css('color', 'red')
        $('#actionButton').html('返回重新认证')
      }
    }

    function jsIdentityBack() {
      if(url){
        window.location.replace(decodeURIComponent(url));
      }else{
        const succ = (pass == 'true')
        if (window.android) {
          if (window.android.jsIdentityBack) {
            window.android.jsIdentityBack(succ);
          }
        } else {
          setupWebViewJavascriptBridge(function (bridge) {
            bridge.callHandler('jsIdentityBack', succ);
          });
        }
      }
    }

    function setupWebViewJavascriptBridge(callback) {
      if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
      if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
      window.WVJBCallbacks = [callback];
      var WVJBIframe = document.createElement('iframe');
      WVJBIframe.style.display = 'none';
      WVJBIframe.src = 'wvjbscheme://__BRIDGE_LOADED__';
      document.documentElement.appendChild(WVJBIframe);
      setTimeout(function () { document.documentElement.removeChild(WVJBIframe) }, 10);
    }

    function getLocationSearch(href = location.href) {
      const theRequest = new Object();
      if (href.indexOf("?") > -1) {
        const hrefStr = href.substr(href.indexOf("?") + 1);
        const arr = hrefStr.split("&");
        arr.filter(item => {
          theRequest[item.split('=')[0]] = item.split('=')[1];
        })
        return theRequest;
      }
      return {};
    }

    (() => {
      function block() {
        setInterval(() => {
          debugger;
        }, 50);
      }
      try {
        block();
      } catch (err) { }
    })();

  </script>
</head>

<body>
  <div>
    <img id="statusIcon" alt="" />
    <span id="statusText">11</span>
    <button id="actionButton" onclick="jsIdentityBack()"></button>
  </div>
</body>
</html>