import { getToken, getGuestId, isJ<PERSON><PERSON> } from "@/utils/data.js";
import md5 from "./md5.js";
import router from "@/router";
import { encryptByRSA, decryptByRSA } from "@/utils/rsa.js";

const isIOS = () => {
  var u = navigator.userAgent;
  return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
};

const isAndroid = () => {
  var u = navigator.userAgent;
  return u.indexOf("Android") > -1 || u.indexOf("Linux") > -1;
};

const METHOD = {
  GET: "GET",
  POST: "POST",
};

function getBasicParams(options) {
  const appSecret = import.meta.env.VITE_APP_SECRE_KEY;
  let appKey = import.meta.env.VITE_APP_KEY;
  const from = localStorage.getItem("from") || "h5";
  let basicParams = {
    appKey: appKey,
    appVersion: "1.0.0",
    channel:import.meta.env.VITE_APP_CHANNEL,
    horse: import.meta.env.VITE_APP_CHANNEL,
    appClient: "web",
    openId: "a77c52e3-06bd-4179-8481-061ac9697282",
    from: from,
    clientId: "a77c52e3-06bd-4179-8481-061ac9697282",
    timestamp: new Date().getTime(),
    net: "wifi",
    versionCode: "100",
    token: getToken(),
    guestId: getGuestId(),
    appCode: "0000002",
    categoryCode: "fenqi",
    cryptoType: "RSA",
  };

  // console.log(`basicParams---${JSON.stringify(basicParams)}--options--${JSON.stringify(options)}--appSecret--${appSecret}`);
  let bodyStr;
  if (options.body) {
    bodyStr =
      Object.keys(options.body).length > 0
        ? JSON.stringify(options.body)
        : "{}";
  } else {
    bodyStr = "";
  }
  const appSign = `${basicParams.appKey}${basicParams.appVersion}${basicParams.appClient}${basicParams.versionCode}${basicParams.timestamp}${basicParams.guestId}${basicParams.token}${bodyStr}${appSecret}`;
  // console.log(`appSign--${appSign}`);
  basicParams.appSign = md5(md5(appSign));
  return basicParams;
}

async function requestAction(url, options, basicParams) {
  let method = METHOD.POST;
  if (options.method) {
    method = options.method;
  }

  // console.log('加密前header----', JSON.stringify(basicParams))
  const requestHeader = await encryptByRSA(
    encodeURIComponent(JSON.stringify(basicParams))
  ); //encodeURIComponent
  // console.log('加密后header----', requestHeader)

  let baseUrl = import.meta.env.VITE_APP_BASE_API;
  return fetch(baseUrl + url, {
    method,
    headers: {
      "Content-Type": "application/json;charset=utf-8",
      requestHeader: requestHeader,
    },
    body: JSON.stringify(options.body), // ? encryptByRSA(JSON.stringify(options.body)) : {}
  })
    .then((res) => {
      return res.text();
    })
    .then((res) => {
      let data = res;
      if (isJSON(res)) {
        data = JSON.parse(res);
      } else {
        let decryptStr = decryptByRSA(data);
        decryptStr = decryptStr.replace(/\+/g, "%20");
        data = JSON.parse(decodeURIComponent(decryptStr)); // decodeURIComponent
        console.log("解密返回res----", data);
      }

      if (data.code == 700 || data.code == 12001) {
        router.push("/signin");
      }
      return data;
    });
}

export function Request(url, options) {
  const basicParams = getBasicParams(options);
  return requestAction(url, options, basicParams);
}

export function RequestUpload(url, formData) {
  const basicParams = getBasicParams({});

  let baseUrl = import.meta.env.VITE_APP_BASE_API;
  return fetch(baseUrl + url, {
    method: "POST",
    headers: {
      // 'Content-Type': 'application/json;charset=utf-8',
      // 'requestHeader': encryptByRSA(JSON.stringify(basicParams))
      requestHeader: encryptByRSA(
        encodeURIComponent(JSON.stringify(basicParams))
      ),
    },
    body: formData,
  })
    .then((res) => {
      return res.text();
    })
    .then((res) => {
      let data = res;
      if (isJSON(res)) {
        data = JSON.parse(res);
      } else {
        let decryptStr = decryptByRSA(data);
        decryptStr = decryptStr.replace(/\+/g, "%20");
        data = JSON.parse(decodeURIComponent(decryptStr)); // decodeURIComponent
        console.log("解密返回res----", data);
      }

      if (data.code == 700 || data.code == 12001) {
        router.push("/signin");
      }
      return data;
    });
}
