import OSS from 'ali-oss'
import { uuid } from './data.js'
import { queryFileToken } from '@/api/common.js'
import { showToast } from '@nutui/nutui'
import { RequestUpload } from '../utils/request.js'
import heic2any from 'heic2any';

let tokenInfo = {}
const uploadAction = async (file) => {

  if (!file) return '';
  const tokenRes = await queryFileToken();

  if (tokenRes.code == 0) {
    tokenInfo = tokenRes.data;
    if (tokenInfo.clientType == 3) {
      return serverUpload(file);
    }
    else if (tokenInfo.clientType == 1) {
      return ossUpload(file)
    }
  } else {
    showToast.text(tokenRes.msg)
    return;
  }
}

const ossUpload = (file) => {
  return new Promise(async (resolve, reject) => {
    const ossInfo = tokenInfo;
    console.log("ossInfo-->" + JSON.stringify(ossInfo));
    const client = new OSS({
      accessKeyId: ossInfo.accessKeyId,
      accessKeySecret: ossInfo.accessKeySecret,
      bucket: ossInfo.bucketName,
      stsToken: ossInfo.securityToken,
      endpoint: ossInfo.baseUrl,
      secure: true,
    });

    let blob = file 
    readFile(blob)
      .then((res) => {
        const objectKey = ossInfo.objectName + '/' + uuid() + '/' + file.name;
        const buffer = new OSS.Buffer(res.target.result);
        client.put(objectKey, buffer)
          .then(result => {
            console.log('oss-jieguo---', result)
            resolve(result.url);
          })
          .catch(err => {
            console.log('err----', err)
            showToast.text(err);
            resolve('')
          })
      })
      .catch(e => {
        resolve('')
      })
  })
};

const serverUpload = async (file) => {
  let blob = file 
  let formData = new FormData()
  formData.append('name', blob, '.png')
  const serverRes = await RequestUpload('/2dc611eae1de4628a0f91200e0dc64b2', formData)
  console.log('serverRes---', serverRes)
  if (serverRes.code == 0) {
    return serverRes.data.filePath
  } else {
    showToast.text(serverRes.msg)
    return ''
  }
}

const toBlob = (urlData, fileType) => {
  let bytes = window.atob(urlData);
  let n = bytes.length;
  let u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bytes.charCodeAt(n);
  }
  return new Blob([u8arr], { type: fileType });
}

const heicToJpg =  async (blob) => {
  return await heic2any({
    blob,
    toType: 'image/jpeg',
    quality: 0.5
  })
}

const readFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = function (event) {
      resolve(event)
    }
  })
}

export default {
  uploadAction
};

