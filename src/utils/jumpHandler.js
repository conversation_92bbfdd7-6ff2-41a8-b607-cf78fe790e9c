import { queryAuthList } from "../api/profile";
import { showToast } from "@nutui/nutui";

import router from "@/router";

const ContactsJumpUrl = "APP/PROFILE/CONTACT_INFO";
const BindCardJumpUrl = "APP/PROFILE/BIND_CARD_CODE";
const PersonalInfoUrl = "APP/PROFILE/PERSONAL_INFO";
const FaceOCRJumpUrl = "APP/PROFILE/FACE_OCR";

const verifyJumpHandler = async (query) => {
  const res = await queryAuthList();
  if (res.code == 0) {
    const nextJumpUrl = res.data.nextJumpUrl;
    switch (nextJumpUrl) {
      case FaceOCRJumpUrl:
        {
          router.push("/identity-info");
        }
        break;
      case PersonalInfoUrl:
        {
          if (isInnerVerify()) {
            router.replace("/basic-info");
          } else {
            router.push("/basic-info");
          }
        }
        break;
      case ContactsJumpUrl:
        {
          if (isInnerVerify()) {
            router.replace("/emergency-info");
          } else {
            router.push("/emergency-info");
          }
        }
        break;
      case BindCardJumpUrl:
        {
          if (isInnerVerify()) {
            router.replace({
              path: "/card-info",
              query: {
                isFirstBind: true,
                qd: query && query.qd,
              },
            });
          } else {
            router.push({
              path: "/card-info",
              query: {
                isFirstBind: true,
                qd: query && query.qd,
              },
            });
          }
        }
        break;
      default:
        break;
    }
  } else {
    showToast.text(res.msg);
  }
};

const isInnerVerify = () => {
  const currentPath = router.currentRoute.value.fullPath;
  console.log("current path ----", currentPath);
  if (
    currentPath.indexOf("identity-info") > -1 ||
    currentPath.indexOf("emergency-info") > -1 ||
    currentPath.indexOf("basic-info") > -1
  ) {
    return true;
  }
  return false;
};

export default {
  verifyJumpHandler,
};
