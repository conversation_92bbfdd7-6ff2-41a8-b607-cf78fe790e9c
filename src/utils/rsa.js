import JSEncrypt from "jsencrypt-ext";

// RSA加密
var encryptor = new JSEncrypt();
// var pubKey = `-----BEGIN PUBLIC KEY-----
// MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDBevKRvqBtQT7koZ7Wj5g3bBp
// CmfTV0zhi9ubTxtUIrW1E6YSIXk1htru13IUT0qWsy/k8mO/2oApiEH4K0VyNeLd
// EhkrLDU/bBcAIJ6hl10KskV6YPedYqy/Zj+Z7AyXhb2gsssUaxhl/skT27Dd3V4Q
// XsmjpH2rK4qu/xk7RwIDAQAB
// -----END PUBLIC KEY-----`

var pubKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgzWUFJf0tW3qOZxrFbvaalTW0xcp6T0MMfxKhGNrARvfEYaJD+yr4OELP6EPltemqzN2zI7UY9AzArYdfyRUPXcLAOXwLLngogkvMpKREW/BFbD7wI6vbOpK+6HNv589EfDcKxlSrAZqWMleufCuwl3q7AAUVGqoUYHvuZHwuVwIDAQAB'

export function getPubKey(){
  return encryptor.getPublicKey();
}

export function setPublicKey(){
  encryptor.setPublicKey(pubKey)//设置公钥
}

export function encryptByRSA(data){
	// return encryptor.encryptLong(data)
  return encryptor.encrypt(data)

}


var decryptor = new JSEncrypt(); 
// var priKey  = '-----BEGIN RSA PRIVATE KEY-----MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKDNZQUl/S1beo5nGsVu9pqVNbTFynpPQwx/EqEY2sBG98RhokP7Kvg4Qs/oQ+W16arM3bMjtRj0DMCth1/JFQ9dwsA5fAsueCiCS8ykpERb8EVsPvAjq9s6kr7oc2/nz0R8NwrGVKsBmpYyV658K7CXersABRUaqhRge+5kfC5XAgMBAAECgYBEB2ErkmzP2Zm50SjkShOORn3YIq2MnSfSi5gIC4nQOrXroRqTBBmjtbmeV7cR4aofllhcx0iAbA9RHJjfDyUXFHmvbKdnHRgoeXLF1ff578sjfh0ExFKnokRqH8NEfBBV404vMJpyisUgXCnEyH1PvVapPe9JHa4zAnvTSvEwAQJBAOH7stuwAgAbCCCJCTv9ujNAt7Di4SVMaNGBIpMVy1LdAWytBdunkc/qSXoSLcsEyFq+CMi0/5QKFJbW/15FvAECQQC2KUtkWE8XI8YVr6OCq+VUbDtN/yoSTa7AC5GMJI+Xd/pOaqidP/OHomvEt3f97zqSsdBnNbkJgLqpW3U2cUpXAkEAv1UcWmTrTKuWdfWQm/p3bG2fGWT+u1W2aausWlxZig8U5a6ZByEZk7AKBhDeNMYX3LyJM2YL/ouKYywliuwAAQJAYpV+o9PXGeLWdS4VA8cb2dCpV9DcaAN6q5yXLI0s2QCpin7WuiO+HI2eXVwdqGQsAvAQpYrBlY8Bdl501P4DCQJBALNBZvJwpmUhT7J9YejTAK4DofEyVj6qEYBSf7/2Kme1oY5pnBOHqLoRPCs2C2opFap+exOYRtXJqZcYyZhOj/I=-----END RSA PRIVATE KEY-----'
// var priKey = `-----BEGIN RSA PRIVATE KEY-----
// MIICXQIBAAKBgQDDBevKRvqBtQT7koZ7Wj5g3bBpCmfTV0zhi9ubTxtUIrW1E6YS
// IXk1htru13IUT0qWsy/k8mO/2oApiEH4K0VyNeLdEhkrLDU/bBcAIJ6hl10KskV6
// YPedYqy/Zj+Z7AyXhb2gsssUaxhl/skT27Dd3V4QXsmjpH2rK4qu/xk7RwIDAQAB
// AoGAJCof7HW3FIB1+RTV3WABu0LA6OmmETnaJuUhhy5nOfXpzjdjj28no/Zq+Ol4
// 3S1K/qEh24nbV4N0Sr9axGN5z/2Jtm/B59zLSXVSdZWPYxHt0HSpRn2ovbr9KaS3
// 7mZ7vlIyh1fY89lkUqFF7M/NaXQVUBXW2G4QTVkifhSBCuECQQD9XF2RxMXBOuCC
// Uwr+iEChl53Q/3RH4EC+7of/UPR6Dc6dS1eDUOD2rgTjYvjvPY+TCvAix4YxrYQv
// zqddYvjRAkEAxQ38s0cyUjPkrr5l/2pZxTLDrYXo94JXcTtkFTshQVUniDnMxOIp
// dnnstjEq6uMMiWsoQcKpjVbaTGUR6U74lwJBALEPI7UDNtBbPRrWvhAzWDeVpYyx
// eanhZl4IhLJA+RlzHLmANaBnnU/HPVNLj3xiITw5oKgtl/KPuJlQalYxHIECQQDD
// Mw/YJ/bZJz6pO0KeuSMkDR15juUwCZXFPSfvQBu5NDls4JTPk5mvGyg5cospErEg
// j13ZhpOexyKH+ra7ftcRAkBMOk8oLWaAk4nYUQm2c/deV3qFyoiqpS0VCKV9+vJY
// 0+MfAA0ceAqond7RlGF4eozegMb4UPQW4M1ZvCxnl5D9
// -----END RSA PRIVATE KEY-----`

var priKey = 'MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKDNZQUl/S1beo5nGsVu9pqVNbTFynpPQwx/EqEY2sBG98RhokP7Kvg4Qs/oQ+W16arM3bMjtRj0DMCth1/JFQ9dwsA5fAsueCiCS8ykpERb8EVsPvAjq9s6kr7oc2/nz0R8NwrGVKsBmpYyV658K7CXersABRUaqhRge+5kfC5XAgMBAAECgYBEB2ErkmzP2Zm50SjkShOORn3YIq2MnSfSi5gIC4nQOrXroRqTBBmjtbmeV7cR4aofllhcx0iAbA9RHJjfDyUXFHmvbKdnHRgoeXLF1ff578sjfh0ExFKnokRqH8NEfBBV404vMJpyisUgXCnEyH1PvVapPe9JHa4zAnvTSvEwAQJBAOH7stuwAgAbCCCJCTv9ujNAt7Di4SVMaNGBIpMVy1LdAWytBdunkc/qSXoSLcsEyFq+CMi0/5QKFJbW/15FvAECQQC2KUtkWE8XI8YVr6OCq+VUbDtN/yoSTa7AC5GMJI+Xd/pOaqidP/OHomvEt3f97zqSsdBnNbkJgLqpW3U2cUpXAkEAv1UcWmTrTKuWdfWQm/p3bG2fGWT+u1W2aausWlxZig8U5a6ZByEZk7AKBhDeNMYX3LyJM2YL/ouKYywliuwAAQJAYpV+o9PXGeLWdS4VA8cb2dCpV9DcaAN6q5yXLI0s2QCpin7WuiO+HI2eXVwdqGQsAvAQpYrBlY8Bdl501P4DCQJBALNBZvJwpmUhT7J9YejTAK4DofEyVj6qEYBSf7/2Kme1oY5pnBOHqLoRPCs2C2opFap+exOYRtXJqZcYyZhOj/I='

export function setPrivateKey(){
  decryptor.setPrivateKey(priKey)//设置秘钥
}

// RSA解密
export function decryptByRSA(data){
	// return decryptor.decryptLong(data)
  return decryptor.decrypt(data)

}
