export default {
  TOKEN_KEY: 'tk',
  TRADENO_KEY: 'tn',
  FLOWID_KEY: 'fi',

  PRIVACY_POLICY_YSXY: "yszc",//隐私协议
  PRIVACY_POLICY_ZCXY: "zcxy", //注册协议
  PRIVACY_POLICY_SMRZSQTYS: "smrzsqtys",    //实名认证授权同意书
  PRIVACY_POLICY_RLYZGRXXSQ: "rlyzgrxxsq",   //人脸认证个人信息授权协议
  PRIVACY_POLICY_GRXXSQS: "grxxsqs",  //个人信息授权书
  PRIVACY_POLICY_SJCXSQSMS: "sjcxsqsms", //数据查询授权说明书
  PRIVACY_POLICY_MGGRXXSQS: "mggrxxsqs",   //敏感个人信息授权书
  PRIVACY_POLICY_JKXZJFXTS: "jkxzjfxts",   //借款须知及风险提示
  PRIVACY_POLICY_SJSYFZXCXSQTK: "sjsyfzxcxsqtk",    //数据使用方征信查询授权条款
  PRIVACY_POLICY_ZHWTKKSQS: "zhwtkksqs",    //账户委托扣款授权书


  HOME_BORROW_STATUS_CAN_BORROW: 13,

  AUTH_USER_PERSONAL_INFO: "APP/PROFILE/PERSONAL_INFO",
  AUTH_USER_CONTACT_INFO: "APP/PROFILE/CONTACT_INFO",
  AUTH_USER_BANK_CARD: "APP/PROFILE/BANK_CARD",
  AUTH_USER_FACE_OCR: "APP/PROFILE/FACE_OCR",
  AUTH_USER_USER_CREDIT: "APP/PROFILE/CREDIT",
  AUTH_USER_CARD_KYC: "APP/PROFILE/KYC",
  AUTH_USER_CARD_KYC2: "APP/PROFILE/KYC2",
  AUTH_USER_CARRIER: "APP/PROFILE/CARRIER",
  AUTH_USER_BANK_CARD_NEW: "APP/PROFILE/BIND_CARD_CODE",

  STATUS_HOME: "APP/RenderTemplate/Product/Home",
  STATUS_HOME_WAIT_REVIEW: "APP/RenderTemplate/Product/Audit",
  STATUS_HOME_LOAN_FAIL: "APP/RenderTemplate/Product/PayFail",
  STATUS_HOME_WAIT_REPAYMENT: "APP/RenderTemplate/Product/RepaymentWatting",
  STATUS_HOME_REPAYMENT_OVERDUE: "APP/RenderTemplate/Product/Overdue",
  STATUS_HOME_LOAN_REFUSE: "APP/RenderTemplate/Product/AuditFail",
  STATUS_HOME_WAIT_FOR_PAY: "APP/RenderTemplate/Product/LoanWaitting",
  STATUS_HOME_CONFIRM_TRADE: "APP/RenderTemplate/Product/ConfirmTrade",
  STATUS_HOME_WAIT_SIGN: "APP/RenderTemplate/Product/WaitSign",

  JUMP_URL_PRODUCT_SHORT_URL: "URL/JS/HOME_INDEX",
  JUMP_URL_ROUTE_USER_DATA: "APP/CLViewControllerUserDataList",
  JUMP_URL_ROUTE_LOAN_REPAYMENT: "APP/CLViewRepay",
  JUMP_URL_ROUTE_LOAN_EXTEND: "APP/CLViewLoanRenewal",
  JUMP_URL_PRODUCT_CARD_LIST: "APP/Product/CardList",
  JUMP_URL_PRODUCT_CONFIRM_TRADE: "APP/Product/ConfirmTrade",
  JUMP_URL_PRODUCT_SIGN_CONTRACT: "APP/Product/SignContract",

  JUMP_URL_PLEDGE_CONFIRM_SMS: "APP/Product/ConfirmTradeSms",
  JUMP_URL_PLEDGE_SIGN_CONTRACT: "APP/Product/SignContract",


  //本息
  BIND_CARD_TYPE_PRINCIPAL_INTEREST:1,
  //担保
  BIND_CARD_TYPE_GUARANTEE:2,

  IS_FIRST_OPEN : "firstopen",
}