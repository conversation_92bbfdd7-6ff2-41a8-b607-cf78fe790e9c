import CryptoJS from 'crypto-js';
// import Encrypt from 'encryptlong'

export function arrayNoNull(array) {
	if (array !== null && array.length) {
		return true;
	} else {
		return false;
	}
}

export function stringNoNull(value) {
	if (value !== null && value !== '' && value !== undefined) {
		return true;
	} else {
		return false;
	}
}

export function secondToDate(date) {
	var h = Math.floor(date / 3600);
	var m = Math.floor((date / 60) % 60);
	var s = Math.floor(date % 60);

	let hn, mn, sn;
	if (h === 0) {
		hn = '';
	} else {
		hn = h + '小时';
	}

	if (m === 0) {
		mn = '';
	} else {
		mn = m + '分钟';
	}

	if (s === 0) {
		sn = '';
	} else {
		sn = s + '秒';
	}

	if (date === 0) {
		return '0分钟';
	} else {
		return hn + mn + sn;
	}
}

export function secondToDateColon(date) {
	var h = Math.floor(date / 3600);
	var m = Math.floor((date / 60) % 60);
	var s = Math.floor(date % 60);

	let hn, mn, sn;
	if (h === 0) {
		hn = '';
	} else {
		if (h > 9) {
			hn = h + ':';
		} else {
			hn = '0' + h + ':';
		}
	}

	if (m > 9) {
		mn = m + ':';
	} else {
		mn = '0' + m + ':';
	}

	if (s > 9) {
		sn = s;
	} else {
		sn = '0' + s;
	}

	if (date === 0) {
		return '00:00';
	} else {
		return hn + mn + sn;
	}
}

// 格式化 YYYY-MM-DD HH:mm
export function formatWholeDate(secs) {
	var t = new Date(secs);
	var year = t.getFullYear();
	var month = t.getMonth() + 1;
	if (month < 10) {
		month = '0' + month;
	}
	var date = t.getDate();
	if (date < 10) {
		date = '0' + date;
	}
	var hour = t.getHours();
	if (hour < 10) {
		hour = '0' + hour;
	}
	var minute = t.getMinutes();
	if (minute < 10) {
		minute = '0' + minute;
	}
	// var second = t.getSeconds()
	// if (second < 10) { second = '0' + second }
	return year + '-' + month + '-' + date + ' ' + hour + ':' + minute;
}

//获取系统当前时间并进行格式化
export function getNewDate() {
	var date = new Date();
	var seperator1 = '-';
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	var strDate = date.getDate();

	if (month >= 1 && month <= 9) {
		month = '0' + month;
	}
	if (strDate >= 0 && strDate <= 9) {
		strDate = '0' + strDate;
	}
	var currentdate = year + seperator1 + month + seperator1 + strDate;
	return currentdate;
}

// 获取已完成订单中时间选择器数据(近六年的选择)
export function getOrderTimeSelectArray() {
	var myDate = new Date();
	var y = myDate.getFullYear();
	let timeArray = [];
	let yearArray = [];
	for (let i = y; i > y - 6; i--) {
		yearArray.push(i + '');
	}
	timeArray[0] = yearArray;
	timeArray[1] = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
	return timeArray;
}

//判断所选时间是否和已选时间相同
export function checkTimeSame(array, month) {
	let timeArray = getOrderTimeSelectArray();
	let selectMonth = timeArray[0][array[0]] + '-' + timeArray[1][array[1]];
	if (selectMonth === month) {
		return true;
	} else {
		return false;
	}
}

export function uuid() {
	var s = [];
	var hexDigits = '0123456789abcdef';
	for (var i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = '4'; // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = '-';

	var uuid = s.join('');
	return uuid;
}

// 将base64转换为文件对象
export function dataURLtoFile(dataurl) {
	const arr = `data:image/png;base64,${dataurl}`.split(',');
	const mime = arr[0].match(/:(.*?);/)[1];
	const bstr = atob(arr[1]);
	let n = bstr.length;
	const u8arr = new Uint8Array(n);
	while (n--) {
		u8arr[n] = bstr.charCodeAt(n);
	}
	let blob = new File([u8arr], 'file', {
		type: mime
	});
	const params = new FormData();
	params.append('file', blob);
	return params;
}

// 检测是否已经过去24小时
export function isNeedShowGpsModal() {
	let gpsIsCheckOneDay = localStorage.getItem('gpsIsCheckOneDay');
	if (gpsIsCheckOneDay === 'true') {
		let gpsCacheTime = localStorage.getItem('gpsCacheTime');
		let currentTime = Date.parse(new Date());
		if (gpsCacheTime) {
			if (currentTime - gpsCacheTime < 24 * 60 * 60 * 1000) {
				return false;
			} else {
				return true;
			}
		} else {
			return false;
		}
	} else {
		return true;
	}
}

export function logout() {
	localStorage.setItem('tk','')
}

export function getToken() {
	return localStorage.getItem('tk') || ''
}

// 是否登录
export function isLogin() {
	return stringNoNull(getToken());
}

export function getGuestId() {
	return localStorage.getItem('gid') || ''
}

export function setGuestId(gid) {
	return localStorage.setItem('gid', gid)
}

export function getHours() {
	var hours = [];
	for (var i = 0; i < 96; i++) {
		var unit = 15;
		var count = parseInt(i / 4);
		var hour;
		if (count < 10) {
			hour = '0' + count;
		} else {
			hour = count;
		}
		var minute = (i % 4) * unit;
		var date;
		if (minute === 0) {
			date = hour + ':' + '00';
		} else {
			date = hour + ':' + minute;
		}
		hours.push(date);
	}
	return hours;
}

export function isJSON(str) {
	if (typeof str == 'string') {
			try {
					JSON.parse(str);
					return true;
			} catch(e) {
					return false;
			}
	}
	console.log('It is not a string!')    
}

//DES加密
export function encryptByDES(message, key){
	var keyHex = CryptoJS.enc.Utf8.parse(key);
	var encrypted = CryptoJS.DES.encrypt(message, keyHex, {
			mode: CryptoJS.mode.ECB,
			padding: CryptoJS.pad.Pkcs7
	});
	return encrypted.ciphertext.toString();
}

//DES解密
export function decryptByDES(ciphertext, key){
	var keyHex = CryptoJS.enc.Utf8.parse(key);
	var decrypted = CryptoJS.DES.decrypt({
			ciphertext: CryptoJS.enc.Hex.parse(ciphertext)
	}, keyHex, {
			mode: CryptoJS.mode.ECB,
			padding: CryptoJS.pad.Pkcs7
	});
	var result_value = decrypted.toString(CryptoJS.enc.Utf8);
	return result_value;
}


