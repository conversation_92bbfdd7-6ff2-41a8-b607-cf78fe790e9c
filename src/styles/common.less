
@primaryColor: #E25B19;
@homeTitleText:#EDDBA9;
@homeMessageBg:#FEF6E5;
@homeStatusBg:#FEF6E5;
@homeAmountText:#5C4A20;

@buttonStartColor: #E9C599;
@buttonEndColor: #df9644;

@homeBGStartColor:#191d28;
@homeBGEndColor:#f6f6f6;

.containerHeight{
  height: calc(100vh - 50px);
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -20px;
  z-index: 10;
}

.tips{
  padding: 0 20px;
  color: #666;
  font-size: 13px;
}

.nut-send-button{
  // background: @primaryColor;
  background: transparent;
  color: @buttonEndColor;
  border: none;
  width: 90px;
}

.nut-timer-button{
  background: transparent;
  color: #333;
  border: none;
  width: 80px;
}

.nut-bg-white{
  background-color: #fff;
}
.nut_bg_main{
  background-color: #E25B19;
}
.nut-common-bg{
  background-color: #F6F6F6;
}
.nut-font-color-main{
  color: #E25B19;
}
.nut-flex-v-center{
  display: flex;
  flex-direction: column;
  align-items: center;
}
.nut-flex-h-center{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.nut-flex-v{
  display: flex;
  flex-direction: column;
}
.nut-flex-h{
  display: flex;
  flex-direction: row;
}
.nut-flex-center{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.nut-font-color-home-text{
  color: @homeTitleText;
}
.not-bg-home-message{
  background-color: @homeMessageBg;
}
.nut-bg-home-status{
  background-color: @homeStatusBg;
}
.nut-font-color-home-amount{
  color: @homeAmountText;
}
.nut_main_button{
  width: 100%;
  height: 48px;
  border-radius: 7px;
  background: linear-gradient(to right,#FAC998,#F6A95D);
  display: flex;
  justify-content: center;
  align-items: center;
  border: none;
}
.nut_dashed {
  height: 1px;
  background: linear-gradient(to right, #ECE3C5, #ECE3C5 5px, transparent 5px, transparent);
  background-size: 10px 100%;
}

.nut-next-btn{
  background: linear-gradient(to right, @buttonStartColor, @buttonEndColor);
  color: @homeAmountText;
  border-radius: 7px;
  border: none;
}
.nut-top-tips{
  background: #fff6f1;
  padding: 8px 12px;
  display: flex;
  flex-direction: row;
}
.nut-bottom-box {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 80px;
  padding: 0 10%;
}

.base_submit_button_gradient{
  background: linear-gradient(to right,#FAC796,#F6A85D);
}

.home-bg-gradient{
  background: linear-gradient(to bottom, @homeBGStartColor, @homeBGEndColor);
}