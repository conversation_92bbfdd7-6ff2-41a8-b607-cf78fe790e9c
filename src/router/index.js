import { createRouter, createWebHashHistory } from "vue-router";


const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'main',
      redirect:'/home',
      component: () => import('../views/main/index.vue'),
      children:[
        {
          path: '/home',
          name: 'home',
          component: () => import('../views/home/<USER>'),
          meta: {
            keepAlive: false //设置页面是否需要使用缓存
          },
        },
        {
          path: '/repayment',
          name: 'repayment',
          component: () => import('../views/repayment/index.vue'),
          meta: {
            keepAlive: false //设置页面是否需要使用缓存
          },
        },
        {
          path: '/mine',
          name: 'mine',
          component: () => import('../views/mine/index.vue'),
          meta: {
            keepAlive: false //设置页面是否需要使用缓存
          },
        },
      ]
    },
    {
      path: '/my-cards',
      name: 'mycards',
      component: () => import('../views/mine/my-cards.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
    {
      path: '/signin',
      name: 'signin',
      component: () => import('../views/signin/index.vue'),
      meta: {
        keepAlive: true //设置页面是否需要使用缓存
      },
    },
    {
      path: '/sms-code',
      name: 'smscode',
      component: () => import('../views/signin/sms-code.vue'),
    },
    {
      path: '/basic-info',
      name: 'basicinfo',
      component: () => import('../views/profile/basic-info.vue'),
      meta: {
        keepAlive: true //设置页面是否需要使用缓存
      },
    },
    {
      path: '/emergency-info',
      name: 'emergencyinfo',
      component: () => import('../views/profile/emergency-info.vue'),
      meta: {
        keepAlive: true //设置页面是否需要使用缓存
      },
    },
    {
      path: '/identity-info',
      name: 'identityinfo',
      component: () => import('../views/profile/identity-info.vue'),
      meta: {
        keepAlive: true //设置页面是否需要使用缓存
      },
    },
    {
      path: '/card-info',
      name: 'bindcard',
      component: () => import('../views/profile/card-info.vue'),
      meta: {
        keepAlive: true //设置页面是否需要使用缓存
      },
    },
    {
      path: '/protocol',
      name: 'protocol',
      component: () => import('../views/common/protocol.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
    {
      path: '/result',
      name: 'result',
      component: () => import('../views/repayment/result.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
    {
      path: '/setting',
      name: 'setting',
      component: () => import('../views/mine/setting.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
    {
      path: '/contract',
      name: 'contract',
      component: () => import('../views/contract/index.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
    {
      path: '/paidlist',
      name: 'paidlist',
      component: () => import('../views/paidlist/index.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
    {
      path: '/payapp',
      name: 'payapp',
      component: () => import('../views/payapp/index.vue'),
      meta: {
        keepAlive: false //设置页面是否需要使用缓存
      },
    },
  ]
})

export default router;