<template>
  <!-- <router-view></router-view> -->
  <router-view v-slot="{ Component }" :key="$route.name">
    <keep-alive>
      <component :is="Component" :key="$route.name"  v-if="$route.meta.keepAlive"/>
    </keep-alive>
    <component :is="Component" :key="$route.name"  v-if="!$route.meta.keepAlive"/>
  </router-view> 
</template>

<script>
import { uuid, getGuestId, setGuestId } from '@/utils/data'
import constant from './utils/constant';
import { RouterLink, RouterView } from "vue-router";

export default {
  data() {
    return {

    };
  },

  mounted(){

    document.title = import.meta.env.VITE_APP_NAME;
    if(!getGuestId()){
      setGuestId(uuid())
    }
    
    localStorage.setItem(constant.IS_FIRST_OPEN,1)
  },

  methods: {
    
  },
};

window.onload = function () {
  document.addEventListener('touchstart', function (e) {
    console.log("1", e.touches.length)
    if (e.touches.length > 1) {
      e.preventDefault()
    }
  })
  document.addEventListener('gesturestart', function (e) {
    console.log("2")
    e.preventDefault()
  })
}
</script>

<style lang="less">
body {
  font-size: 16px;
  background-color: #f8f8f8;
  -webkit-font-smoothing: antialiased;
  margin: 0px
}
</style>
