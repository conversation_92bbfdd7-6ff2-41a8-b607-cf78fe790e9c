import { Request } from '../utils/request.js'

export function queryAuthList() {
  return Request(`/489929859098497bb81a8dbd141f80d3`,{
    method: 'get'
  })
}

export function queryPersonalInfo() {
  return Request(`/bdef0cb4f1ec4dd7b2a5f79f8e9fd143`,{
    method: 'get'
  })
}

export function savePersonalInfo(params) {
  return Request('/ee2715fe2f4646388a8967ba399bfdd4',{
    method: 'post',
    body: { ...params }
  })
}

export function queryContactsInfo() {
  return Request(`/9fabff71833e45678521e10fb045bd53`,{
    method: 'get'
  })
}

export function saveContactsInfo(params) {
  return Request('/01814d2fea7f4c31b6e5667dddb81e37',{
    method: 'post',
    body: { ...params }
  })
}

export function queryBankInfo() {
  return Request(`/a91fbd29261140809fb3c3dbb26e91ba/AuthUserBank`,{
    method: 'get'
  })
}

export function saveBankInfo(params) {
  return Request('/80d4ba7039ad41f5bdf001be0e075ef8',{
    method: 'post',
    body: { ...params }
  })
}

export function faceCardUpload(params) {
  return Request('/f46d5bf9e8fd4aa884e6ce5a9621b8c6',{
    method: 'post',
    body: { ...params }
  })
}


// 获取人脸信息
export function queryFaceConfig() {
  return Request(`/8871db1fe492492f9a465b9da8e2621d`,{
    method: 'get'
  })
}

// 上传身份证至服务端进行ocr识别
export function queryOCRUpload(params){
  return Request('/fe27f126d85b4d31a0789ba32a40c488',{
    method: 'post',
    body: { ...params }
  })
}

// ocr识别信息提交
export function queryOCRSubmit(params){
  return Request('/b139bd3ecc6b4f7898cf84a2e72ee2b9',{
    method: 'post',
    body: { ...params }
  })
}

// 人脸照片比对
export function queryFaceComparision(params){
  return Request('/a0f06e5da4d748d7b96cf30cbaceca54',{
    method: 'post',
    body: { ...params }
  })
}

