import { Request } from '../utils/request.js'

// 绑卡请求-发码
export async function queryBindCardRequest(params) {
  return Request(`/385a71eb89f84c3093c73d81c18db7b5`,{
    method: 'post',
    body: { ...params }
  })
}

// 绑卡重发验证码
export async function queryBindCardResendSms(params) {
  return Request(`/af5ef4737d9a469ba86a778314abce5f`,{
    method: 'post',
    body: { ...params }
  })
}

// 绑卡确认
export async function queryBindCardConfirm(params) {
  return Request(`/********************************`,{
    method: 'post',
    body: { ...params }
  })
}

// 卡bin查询
export async function queryPayCardBin(params) {
  return Request(`/ffbea4191f6e4a0db382b292cccf38cf`,{
    method: 'post',
    body: { ...params }
  })
}

// 获取支持银行卡 
export async function querySupportBankList() {
  return Request(`/2b7be5a46bad4f08a93dd635631f7828`,{
    method: 'get'
  })
}

// 获取协议
export async function queryProtocolContent(key) {
  return Request(`/1a99fb9ad8534512af0ede2979dfd68f/${key}`,{
    method: 'get'
  })
}

// 发单
export async function queryApplyOrder(params) {
  return Request(`/595f12a07ec547ef97189143f422a4ea`,{
    method: 'post',
    body: { ...params }
  })
}