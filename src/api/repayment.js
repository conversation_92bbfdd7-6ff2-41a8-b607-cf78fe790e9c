import { Request } from '../utils/request.js'

//get
export async function queryPayInfo(key) {
  return Request(`/029239205cd04f4b82dc09c7f291fde5/${key}`,{
    method: 'get'
  })
}

// post
export async function queryBindCardRequest(key, params) {
  return Request(`/********************************/${key}`,{
    method: 'post',
    body: { ...params }
  })
}

export async function orderBillRepaymentList() {
  return Request(`/aa05e9df2dc64621acfd92dc159d989f`,{
    method: 'get'
  })
}

export async function orderBillPaidList() {
  return Request(`/d0939d28eac240d497df89672f438cc8`,{
    method: 'get'
  })
}

export async function orderBillDetail(tradeNo) {
  return Request(`/9616bd02c882499eb8b561c19d1ff602?tradeNo=${tradeNo}`,{
    method: 'get'
  })
}

export async function payClientCharges(params) {
  return Request(`/b244b865e6ab42809744261f2d8ca11a`,{
    method: 'post',
    body: { ...params }
  })
}

export async function cashierInfo(key) {
  return Request(`/029239205cd04f4b82dc09c7f291fde5/${key}`,{
    method: 'get'
  })
}

export async function cashierDoPay(key,params) {
  return Request(`/e6e47205e436470b917283476242a850/${key}`,{
    method: 'post',
    body: { ...params }
  })
}

export async function cashierStatus(key) {
  return Request(`/8d4295c8a823401dbfdd2e092aa0015e/${key}`,{
    method: 'get'
  })
}




export async function checkRepeatCard(key, params) {
  return Request(`/c1e0be9e3393b662f5758d881a6abb4f/${key}`, {
    method: "post",
    body: { ...params },
  });
}