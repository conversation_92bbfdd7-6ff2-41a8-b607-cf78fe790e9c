import { Request } from '../utils/request.js'

//get
export async function queryPayInfo(key) {
  return Request(`/029239205cd04f4b82dc09c7f291fde5/${key}`,{
    method: 'get'
  })
}

// post
export async function queryBindCardRequest(key, params) {
  return Request(`/********************************/${key}`,{
    method: 'post',
    body: { ...params }
  })
}

// 首页
export async function userHome() {
  return Request(`/2caf302f1bdc42a8ab31f85f10a60227`,{
    method: 'post'
  })
}

export async function orderBorrowAgainConfirm() {
  return Request(`/53f50b9774be439a8edbb2aeedba8360`,{
    method: 'get'
  })
}

export async function userPersonalCenter() {
  return Request(`/65cebc44743f49dd9ffbd48bb4d1989e`,{
    method: 'get'
  })
}

export async function orderContractDetail(tradeNo) {
  return Request(`/db27784f8f344a55b4e88292f39f7712?tradeNo=${tradeNo}`,{
    method: 'get'
  })
}

// post
export async function orderContractRequest(params) {
  return Request(`/80a4f9ee9bfb4f9090298db60603b56b`,{
    method: 'post',
    body: { ...params }
  })
}

export async function payBindCardRequest(params) {
  return Request(`/385a71eb89f84c3093c73d81c18db7b5`,{
    method: 'post',
    body: { ...params }
  })
}
export async function payBindCardResendSms(params) {
  return Request(`/af5ef4737d9a469ba86a778314abce5f`,{
    method: 'post',
    body: { ...params }
  })
}
export async function payBindCardConfirm(params) {
  return Request(`/********************************`,{
    method: 'post',
    body: { ...params }
  })
}
export async function getContractUrl(tradeNo) {
  return Request(`/cdbdf865d1df47909d3e1c95583bb2f7?tradeNo=${tradeNo}`,{
    method: 'get'
  })
}
export async function getCustomerServiceUrl() {
  return Request(`/33a192b399414501a2d83be2ab62448e`,{
    method: 'get'
  })
}
