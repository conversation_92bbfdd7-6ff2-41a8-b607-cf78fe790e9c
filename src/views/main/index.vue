<template>
  <div>
    <router-view :key="$route.fullPath" >
    </router-view>
    <TabBar/>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "@nutui/nutui";
import TabBar from '../../components/Tabbar/index.vue'

export default {
  setup() {

    return {
      
    }
  },

  mounted() {
    
  },

  components: {
    TabBar,
    
  }
}
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";

</style>