<template>
  <div style="width: 100%" class="nut-flex-v-center">
    <img
      src="../../../assets/imagesnew/auth_wait_for_pay_bg.png"
      style="width: 100%; height: 80px"
      alt=""
    />

    <div style="width: 100%; margin-top: 15px" class="nut-flex-v-center">
      <span style="font-size: 13px" class="nut-font-color-home-amount">{{
        data.tips
      }}</span>
      <span
        style="font-size: 17px; margin-top: 15px"
        class="nut-font-color-home-amount"
        >{{ data.bigTips }}</span
      >

      <div
        style="
          width: 100%;
          padding: 0 20px;
          box-sizing: border-box;
          margin-top: 10px;
        "
      >
        <div
          v-if="data.buttonJumpUrl"
          @click="clickSign"
          style="
            width: 100%;
            height: 45px;
            border-radius: 7px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            background: linear-gradient(to right, #f3e2b3, #dcc385);
          "
        >
          <span style="font-size: 16px" class="nut-font-color-home-amount">{{
            data.buttonTxt
          }}</span>
        </div>
      </div>
    </div>

    <div style="width: 100%; height: 20px"></div>
    <AuthStepsView :data="data.borrowStepVOList"></AuthStepsView>
    <repeatCard
      ref="repeatCardRef"
      @success="success"
      :flowId="flowId"
    ></repeatCard>
  </div>
</template>

<script>
import { useRouter } from "vue-router";
import { ref, reactive, watch } from "vue";
import { queryCheckReplaceCard } from "../../../api/common";
import constant from "../../../utils/constant";
import repeatCard from "../repeatCard.vue";
import AuthStepsView from "../../../components/auth-steps/index.vue";

export default {
  props: {
    data: Object,
  },
  setup(props) {
    const flowId = ref("");
    const repeatCardRef = ref();
    const router = useRouter();

    const clickSign = async () => {
      const res = await queryCheckReplaceCard();
      if (res.data != null) {
        if (res.data.flowId) {
          flowId.value = res.data.flowId;
          repeatCardRef.value.open();
        }
      }else{
        success()
      }
    };
    const success = () =>
      router.push({
        name: "contract",
        query: {
          type: props.data.buttonJumpUrl,
          tradeNo: props.data.tradeNo,
        },     
      });
    return {
      clickSign,
      flowId,
      repeatCardRef,
      success,
    };
  },

  mounted() {},

  components: {
    AuthStepsView,
    repeatCard,
  },
};
</script>
<style lang="less" scoped>
// @import "../../../styles/common.less";
</style>
