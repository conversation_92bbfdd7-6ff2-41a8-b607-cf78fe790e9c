<template>
    <div style="width: 100%;" class="nut-flex-v-center">

        <div style="width: 100%;" class="nut-flex-v-center">
            <span style="font-size: 17px;" class="nut-font-color-home-amount">{{ data.tips }}</span>
            <span style="font-size: 13px;margin-top: 15px;" class="nut-font-color-home-amount">{{ data.bigTips }}</span>
            <span style="font-size: 10px;color: #B6AC90;margin-top: 5px">{{ data.miniTips }}</span>

            <div class="nut_dashed" style="width: 100%;margin-top: 20px;"></div>

            <div style="width: 100%;display: flex;flex-direction: row;margin-top: 15px;">

                <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                    <img src="../../../assets/imagesnew/home_tip1.png" alt="" style="width: 30px;width: 30px;">
                    <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">安全合规</span>
                </div>
                <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                    <img src="../../../assets/imagesnew/home_tip2.png" alt="" style="width: 30px;width: 30px;">
                    <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">息费透明</span>
                </div>
                <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                    <img src="../../../assets/imagesnew/home_tip3.png" alt="" style="width: 30px;width: 30px;">
                    <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">期限灵活</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref, reactive, watch } from "vue";


export default {
    props: {
        data: Object,
    },
    setup(props) {
        const router = useRouter();

        return {
            
        };
    },

    mounted() {

    },
}
</script>
<style lang="less" scoped>
// @import "../../../styles/common.less";
</style>
