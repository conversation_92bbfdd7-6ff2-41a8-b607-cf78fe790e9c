<template>
  <div>
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

    <nut-pull-refresh
      v-model="refreshLoading"
      @refresh="onRefresh"
      style="
        height: 100vh;
        width: 100%;
        position: relative;
        background-color: #f6f6f6;
        overflow-y: scroll;
      "
    >
      <div style="width: 100%; height: 100%">
        <div
          class="home-bg-gradient"
          style="
            height: 400px;
            width: 100%;

            position: absolute;
          "
        ></div>

        <div
          style="
            height: 100%;
            width: 100%;
            padding: 15px;
            position: absolute;
            flex-direction: column;
            box-sizing: border-box;
          "
        >
          <div
            style="
              display: flex;
              width: 100%;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div
              style="
                display: flex;
                flex-direction: row;
                margin-top: 20px;
                align-items: flex-end;
              "
            >
              <span
                style="font-size: 16px; font-weight: 900"
                class="nut-font-color-home-text"
                >{{ appName }}</span
              >
              <span
                style="font-size: 12px; margin-left: 5px"
                class="nut-font-color-home-text"
                >安心借 放心用</span
              >
            </div>
            <div
              @click="clickCustomerService"
              style="
                width: 40px;
                height: 40px;
                flex-direction: column;
                align-items: center;
                display: flex;
                justify-content: flex-end;
              "
            >
              <img
                src="../../assets//imagesnew/customer_service_home.png"
                alt=""
                style="width: 20px; height: 20px"
              />
            </div>
          </div>

          <div
            style="
              width: 100%;
              height: 45px;
              padding: 0 10px;
              display: flex;
              flex-direction: row;
              align-items: center;
              border-radius: 10px;
              box-sizing: border-box;
              margin-top: 20px;
            "
            class="not-bg-home-message home_message"
          >
            <span
              style="
                font-size: 12px;
                color: white;
                border-radius: 3px;
                background-color: #f02f33;
                padding: 1px 0px;
                min-width: 35px;
                text-align: center;
              "
              >公告</span
            >
            <div style="flex: 1">
              <nut-noticebar
                direction="vertical"
                :list="homeMessages"
                :speed="10"
                :stand-time="2000"
                background="rgba(0,0,0,0)"
                color="#000000"
                style="font-size: 10px"
              ></nut-noticebar>
            </div>
          </div>

          <div style="width: 100%; margin-top: 10px">
            <div
              style="
                display: flex;
                flex-direction: column;
                padding: 15px 20px 15px 20px;
                align-items: center;
                width: 100%;
                box-sizing: border-box;
                border-radius: 10px;
              "
              class="nut-bg-home-status"
            >
              <HomeView
                :data="centerVo"
                v-if="homeViewShow"
                @refreshHome="onRefresh"
              ></HomeView>
              <HomeAuthView
                :data="centerVo"
                v-if="homeAuthViewShow"
              ></HomeAuthView>
              <HomeRefuseView
                :data="centerVo"
                v-if="homeLoanRefuseViewShow"
              ></HomeRefuseView>
              <HomeSignView
                :data="centerVo"
                v-if="homeConfirmSignViewShow"
              ></HomeSignView>
              <HomeWaitForPayView
                :data="centerVo"
                v-if="homeWaitForPayViewShow"
              ></HomeWaitForPayView>
            </div>
          </div>

          <img
            v-if="refuseSlideshow && refuseSlideshow.imgUrl"
            :src="refuseSlideshow.imgUrl"
            alt=""
            style="
              width: 100%;
              height: 150px;
              border-radius: 10px;
              margin-top: 10px;
            "
          />

          <div style="width: 100%; margin-top: 10px" class="nut-flex-center">
            <span style="font-size: 14px; color: black">平台优势</span>
          </div>

          <img
            src="../../assets/imagesnew/home_bottom_tip.png"
            alt=""
            style="width: 100%; height: 120px; margin-top: 3px"
          />

          <nut-swiper
            :is-preventDefault="false"
            :init-page="0"
            :auto-play="3000"
            pagination-visible
            pagination-color="#F6A95D"
            :height="80"
            pagination-unselected-color="#FFFFFF"
            style="border-radius: 7px; margin-top: 10px"
            :style="{
              display:
                slideshowVOList && slideshowVOList.length > 0 ? 'flex' : 'none',
            }"
          >
            <nut-swiper-item
              v-for="(item, index) in slideshowVOList"
              :key="index"
              style="height: 80px"
            >
              <img
                @click="clickBanner(item)"
                :src="item.imgUrl"
                alt=""
                style="height: 100%; width: 100%"
                draggable="false"
              />
            </nut-swiper-item>
          </nut-swiper>
          <div style="width: 100%; height: 80px"></div>
        </div>
      </div>
    </nut-pull-refresh>

    <nut-overlay v-model:visible="authTipShow" :close-on-click-overlay="false">
      <div style="width: 100%; height: 100%; position: relative">
        <div
          style="
            width: 100%;
            position: absolute;
            bottom: 0px;
            border-top-right-radius: 20px;
            border-top-left-radius: 20px;
            background-color: white;
            padding: 30px;
            box-sizing: border-box;
          "
          class="nut-flex-v"
        >
          <span style="font-size: 16px; color: black"
            >欢迎来到{{ appName }}</span
          >
          <span style="font-size: 16px; color: black">现在即可申请额度</span>

          <div class="nut-flex-h-center" style="margin-top: 30px">
            <span style="font-size: 13px; color: #999999">预估可借</span>
            <img
              src="../../assets/imagesnew/gantanhao_grey_small.png"
              style="width: 15px; height: 15px; margin-left: 5px"
              alt=""
            />
          </div>
          <span
            style="font-size: 35px; font-weight: bolder"
            class="nut-font-color-home-amount"
            >{{ centerVo.maxAmount }}</span
          >

          <div style="width: 100%; box-sizing: border-box; margin-top: 40px">
            <div @click="clickApplyNow" class="nut_main_button">
              <span style="font-size: 16px; color: black">{{
                centerVo.buttonTxt
              }}</span>
            </div>
          </div>

          <span
            @click="clickNotBorrowNow"
            style="
              font-size: 13px;
              color: #999999;
              padding: 15px 0px;
              text-align: center;
              width: 100%;
            "
            >暂不申请,先逛逛</span
          >

          <img
            src="../../assets/imagesnew/paobu.png"
            style="
              width: 130px;
              height: 130px;
              position: absolute;
              right: 20px;
              top: -30px;
            "
            alt=""
          />
        </div>
      </div>
    </nut-overlay>
  </div>
</template>

<script>
import Loading from "vue-loading-overlay";
import "vue-loading-overlay/dist/css/index.css";

import { useRouter } from "vue-router";
import { ref } from "vue";
import { showToast } from "@nutui/nutui";
import { getToken } from "../../utils/data";

import {
  userHome,
  getCustomerServiceUrl,
  orderBorrowAgainConfirm,
} from "../../api/main";

import constant from "../../utils/constant";

import HomeView from "./views/homeview.vue";
import HomeAuthView from "./views/home-auth-view.vue";
import HomeRefuseView from "./views/home-refuse-view.vue";
import HomeSignView from "./views/home-sign-view.vue";
import HomeWaitForPayView from "./views/home-wait-for-pay.vue";

import { decryptByDES } from "@/utils/data";

import JumpHandler from "../../utils/jumpHandler";

export default {
  setup() {
    const loading = ref(false);
    const refreshLoading = ref(false);
    const offset = ref({
      x: window.innerWidth - 70,
      y: window.innerHeight / 2,
    });

    const centerVo = ref({});
    const isHomeView = ref(true);

    const appName = ref(import.meta.env.VITE_APP_NAME);
    const homeMessages = ref([]);
    const slideshowVOList = ref([]);

    const refuseSlideshow = ref({
      imgUrl: "",
      jumpUrl: "",
      title: "",
    });

    const homeViewShow = ref(false);
    const homeAuthViewShow = ref(false);
    const homeWaitForPayViewShow = ref(false);
    const homeLoanRefuseViewShow = ref(false);
    const homeConfirmSignViewShow = ref(false);

    const authTipShow = ref(false);
    const serviceUrl = ref();

    const onRefresh = () => {
      getUserHome();
      getServiceUrl();
    };

    const getUserHome = async () => {
      const res = await userHome();
      refreshLoading.value = false;
      if (res.code == 0) {
        if (res.data) {
          const centerVoData = res.data;

          const isFirstOpen = localStorage.getItem(constant.IS_FIRST_OPEN);

          console.log("isFirstOpen-getUserHome-" + isFirstOpen);

          if (
            isFirstOpen == 1 &&
            centerVoData.buttonJumpUrl == constant.JUMP_URL_ROUTE_USER_DATA
          ) {
            authTipShow.value = true;
          } else {
            authTipShow.value = false;
          }

          refuseSlideshow.value = centerVoData.refuseSlideshow;

          const homeMessagesData = centerVoData.homeMessages;
          homeMessages.value = homeMessagesData;

          slideshowVOList.value = centerVoData.slideshowVOList || [];

          centerVo.value = centerVoData;

          if (centerVoData.templateCode == constant.STATUS_HOME) {
            isHomeView.value = true;
          } else {
            isHomeView.value = false;
          }

          if (centerVoData.templateCode == constant.STATUS_HOME) {
            homeViewShow.value = true;
            homeAuthViewShow.value = false;
            homeWaitForPayViewShow.value = false;
            homeLoanRefuseViewShow.value = false;
            homeConfirmSignViewShow.value = false;
          }
          if (centerVoData.templateCode == constant.STATUS_HOME_WAIT_REVIEW) {
            homeViewShow.value = false;
            homeAuthViewShow.value = true;
            homeWaitForPayViewShow.value = false;
            homeLoanRefuseViewShow.value = false;
            homeConfirmSignViewShow.value = false;
          }
          if (
            centerVoData.templateCode == constant.STATUS_HOME_WAIT_FOR_PAY ||
            centerVoData.templateCode == constant.STATUS_HOME_LOAN_FAIL
          ) {
            homeViewShow.value = false;
            homeAuthViewShow.value = false;
            homeWaitForPayViewShow.value = true;
            homeLoanRefuseViewShow.value = false;
            homeConfirmSignViewShow.value = false;
          }
          if (centerVoData.templateCode == constant.STATUS_HOME_LOAN_REFUSE) {
            homeViewShow.value = false;
            homeAuthViewShow.value = false;
            homeWaitForPayViewShow.value = false;
            homeLoanRefuseViewShow.value = true;
            homeConfirmSignViewShow.value = false;
          }
          if (
            centerVoData.templateCode == constant.STATUS_HOME_CONFIRM_TRADE ||
            centerVoData.templateCode == constant.STATUS_HOME_WAIT_SIGN
          ) {
            homeViewShow.value = false;
            homeAuthViewShow.value = false;
            homeWaitForPayViewShow.value = false;
            homeLoanRefuseViewShow.value = false;
            homeConfirmSignViewShow.value = true;
          }
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg);
        }
      }
    };

    const getServiceUrl = async () => {
      const res = await getCustomerServiceUrl();
      if (res.code == 0) {
        const data = res.data;
        if (data) {
          serviceUrl.value = data.customerServiceUrl;
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg);
        }
      }
    };

    const clickApplyNow = async () => {
      authTipShow.value = false;
      localStorage.setItem(constant.IS_FIRST_OPEN, 0);

      console.log(
        "isFirstOpen-clickApplyNow-" +
          localStorage.getItem(constant.IS_FIRST_OPEN)
      );

      if (getToken()) {
        loading.value = true;
        const res = await orderBorrowAgainConfirm();
        loading.value = false;
        console.log("orderBorrowAgainConfirm---finished");

        if (res.code == 0) {
          const data = res.data;
          console.log("data.jumpUrl-->" + data.jumpUrl);
          if (data.jumpUrl) {
            console.log("if (data.jumpUrl) {");
            JumpHandler.verifyJumpHandler();
          } else {
            console.log("if (data.jumpUrl) {---getUserHome");
            getUserHome();
          }
        } else {
          if (res.msg) {
            showToast.text(res.msg);
          }
        }
      } else {
        router.push("/signin");
      }
    };

    const clickNotBorrowNow = () => {
      localStorage.setItem(constant.IS_FIRST_OPEN, 0);

      console.log(
        "isFirstOpen-clickNotBorrowNow-" +
          localStorage.getItem(constant.IS_FIRST_OPEN)
      );

      authTipShow.value = false;
    };

    const clickBanner = (data) => {
      console.log("data.jumpUrl-->" + data.jumpUrl);
      if (data.jumpUrl) {
        window.location.href = data.jumpUrl;
      }
    };

    const clickCustomerService = () => {
      if (serviceUrl.value) {
        window.location.href = serviceUrl.value;
      }
    };

    return {
      loading,
      onRefresh,
      refreshLoading,
      getUserHome,
      isHomeView,
      centerVo,
      homeViewShow,
      homeAuthViewShow,
      homeWaitForPayViewShow,
      homeLoanRefuseViewShow,
      homeConfirmSignViewShow,
      offset,
      clickCustomerService,
      appName,
      homeMessages,
      slideshowVOList,
      clickBanner,
      getServiceUrl,
      serviceUrl,
      authTipShow,
      clickApplyNow,
      clickNotBorrowNow,
      refuseSlideshow,
    };
  },

  async mounted() {
    if (this.$route.query.t) {
      const val = decryptByDES(
        this.$route.query.t,
        import.meta.env.VITE_APP_DES_KEY
      );
      const { token = "", guestId = "", from = "" } = JSON.parse(val);
      // console.log("解密后----", token, guestId, from);
      localStorage.setItem("tk", token);
      localStorage.setItem("gid", guestId);
      localStorage.setItem("from", from);
      await this.$nextTick();
      if (this.$route.query.qd == "jiurongjiekuan") {
        JumpHandler.verifyJumpHandler({ qd:this.$route.query.qd });
      }
    }

    this.getUserHome();
    this.getServiceUrl();
  },

  components: {
    Loading,
    HomeView,
    HomeAuthView,
    HomeRefuseView,
    HomeSignView,
    HomeWaitForPayView,
  },
};
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";

.home_message {
  :deep(.nut-noticebar__vertical) {
    padding: 0 5px;
  }
  :deep(.nut-noticebar__vertical-list) {
    font-size: 12px;
  }
}
</style>
