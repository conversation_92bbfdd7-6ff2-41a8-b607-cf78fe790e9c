<template>
  <nut-dialog
    teleport="#app"
    title="本人还款操作确认"
    v-model:visible="visible"
    :noOkBtn="true"
    :noCancelBtn="true"
    :closeOnClickOverlay="false"
  >
    <nut-form>
      <nut-form-item prop="smsCode" :model-value="formData">
        <nut-input
          v-model="formData.smsCode"
          placeholder="请输入验证码"
          type="text"
        >
          <template #right>
            <nut-button size="small" @click="getSms">{{ timeText }}</nut-button>
          </template>
        </nut-input>
      </nut-form-item>
      <nut-form-item>
        <div
          style="
            width: 100%;
            height: 45px;
            border-radius: 7px;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          class="base_submit_button_gradient"
          @click="confirm"
        >
          <span style="font-size: 16px" class="nut-font-color-home-amount"
            >确认</span
          >
        </div>
      </nut-form-item>
    </nut-form>
  </nut-dialog>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { queryBindCardConfirm } from "../../api/index";
import { checkRepeatCard, payClientCharges } from "../../api/repayment";
import { useRouter } from "vue-router";
import { showToast } from "@nutui/nutui";
const router = useRouter();
const visible = ref(false);
const emits = defineEmits(["update:flowId", "success"]);
const props = defineProps({
  flowId: {
    type: String,
    default: "",
  },
});
const formData = ref({
  orderType: 1,
  smsCode: "",
});

const loading = ref(false);
const confirm = async () => {
  try {
    if (!formData.value.smsCode) {
      showToast.text("请输入验证码");
      return;
    }
    if (loading.value) return;
    loading.value = true;
    const data = await queryBindCardConfirm({
      flowId: props.flowId,
      orderType: 1,
      smsCode: formData.value.smsCode,
    });

    emits("success");
  } catch (e) {
    console.log(e);

    // showToast.text(res.msg);
  } finally {
    loading.value = false;
  }
};

// if(flowId.value){
//   params.flowId = flowId.value
// }

// 发送验证码
// loading.value = true;
// let res = await queryBindCardRequest(params);

const colse = () => {
  visible.value = false;
};

const open = () => {
  visible.value = true;
};

let time = ref(60);
let timeText = ref("获取验证码");
const getSms = async () => {
  try {
    if (time.value != 0) {
      reutrn;
    }
    const res = await checkRepeatCard(props.bindId);
    time.value = 60;
    if (res.data.flowId) {
      emits("update:flowId", res.data.flowId);
    }
  } catch (e) {}
};
let t = null;
onMounted(() => {
  t = setInterval(() => {
    if (time.value - 1 == -1) {
      timeText.value = "获取验证码";
      return;
    }
    timeText.value = `${time.value}s后重新获取`;
    time.value -= 1;
  }, 1000);
});

onUnmounted(() => {
  clearInterval(t);
});

defineExpose({
  colse,
  open,
});
</script>

<style scoped></style>
