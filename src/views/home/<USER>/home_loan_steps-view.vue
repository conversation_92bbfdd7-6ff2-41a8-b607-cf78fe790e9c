<template>
    <div style="width: 100%;" class="nut-flex-v-center">

        <span style="font-size: 13px;" class="nut-font-color-home-amount">授信额度(元)</span>

        <div style="width: 100%;display: flex;flex-direction: column;align-items: center;position: relative;">
            <span style="font-size: 50px;font-weight: bolder;" class="nut-font-color-home-amount">{{ data.maxAmount}}</span>
        </div>

        <div style="width: 100%;margin-top: 20px;" class="nut-flex-v-center">
            <span style="font-size: 17px;" class="nut-font-color-home-amount">{{data.tips}}</span>
            <span style="font-size: 13px;margin-top: 5px;" class="nut-font-color-home-amount">{{data.bigTips}}</span>
        </div>

    </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref, reactive, watch } from "vue";

import constant from '../../../utils/constant';

export default {
    props: {
        data: Object,
    },
    setup(props) {
        const router = useRouter();

        return {

        };
    },

    mounted() {

    },
}
</script>
<style lang="less" scoped>
// @import "../../../styles/common.less";
</style>
