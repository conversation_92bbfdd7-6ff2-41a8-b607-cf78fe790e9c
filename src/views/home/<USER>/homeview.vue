<template>
    <div style="width: 100%;position: relative;" class="nut-flex-v-center">

        <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

        <span style="font-size: 13px;" class="nut-font-color-home-amount">最高可借额度(元)</span>

        <div style="width: 100%;display: flex;flex-direction: column;align-items: center;position: relative;">
            <span style="font-size: 50px;font-weight: bolder;" class="nut-font-color-home-amount">{{ data.maxAmount
                }}</span>
        </div>

        <span style="font-size: 10px;color: #B6AC90;">{{ data.miniTips }}</span>

        <div class="nut_dashed" style="width: 100%;margin-top: 20px;"></div>

        <div style="width: 100%;display: flex;flex-direction: row;margin-top: 15px;">
            <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                <img src="../../../assets/imagesnew/home_tip1.png" alt="" style="width: 30px;width: 30px;">
                <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">安全合规</span>
            </div>
            <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                <img src="../../../assets/imagesnew/home_tip2.png" alt="" style="width: 30px;width: 30px;">
                <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">息费透明</span>
            </div>
            <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                <img src="../../../assets/imagesnew/home_tip3.png" alt="" style="width: 30px;width: 30px;">
                <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">期限灵活</span>
            </div>
        </div>

        <span style="font-size: 11px;color: #F02F33;margin-top: 20px;">新用户下款率高达90%</span>

        <div style="width: 100%;padding: 0 20px;box-sizing: border-box;margin-top: 10px;">

            <div @click="clickApplyNow"
                style="width: 100%;height: 45px;border-radius: 7px;display: flex;justify-content: center;align-items: center;position: relative;background: linear-gradient(to right,#F3E2B3,#DCC385);">
                <span style="font-size: 16px;font-weight: 900;" class="nut-font-color-home-amount">{{ data.buttonTxt
                    }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref, reactive, watch } from "vue";
import { showToast } from "@nutui/nutui";

import { orderBorrowAgainConfirm } from '../../../api/main'
import constant from '../../../utils/constant';
import { getToken } from '../../../utils/data'

import JumpHandler from '../../../utils/jumpHandler'

import Loading from 'vue-loading-overlay';
export default {
    props: {
        data: Object,
    },
    setup(props,context) {
        const router = useRouter();

        const loading = ref(false)

        const clickApplyNow = async () => {
            if (getToken()) {
                loading.value = true
                const res = await orderBorrowAgainConfirm()
                loading.value = false
                if (res.code == 0) {
                    if(res.data && res.data.jumpUrl){
                        JumpHandler.verifyJumpHandler()
                    } else {
                        context.emit('refreshHome')
                    }
                } else {
                    if (res.msg) {
                        showToast.text(res.msg)
                    }
                }
            } else {
                router.push('/signin')
            }
        }

        // watch(
        //     () => props.data,
        //     (newVal) => {
        //         centerVo.value = newVal
        //     }
        // );

        return {
            clickApplyNow,

            loading
        };
    },

    mounted() {
        console.log("localStorage-->first-opne->" + localStorage.getItem('firstopen'));

    },
    components: {
        Loading
    }
}
</script>
<style lang="less" scoped>
// @import "../../../styles/common.less";
</style>
