<template>
    <div style="width: 100%;height: 100%;position: relative;" class="nut-flex-v-center">

        <span style="font-size: 13px;" class="nut-font-color-home-amount">{{data.tips}}</span>

        <div style="width: 100%;display: flex;flex-direction: column;align-items: center;position: relative;">
            <span style="font-size: 16px;font-weight: bolder;margin-top: 15px;" class="nut-font-color-home-amount">{{data.bigTips}}</span>
        </div>

        <span style="font-size: 10px;color: #B6AC90;margin-top: 15px;">{{ data.miniTips }}</span>

        <div class="nut_dashed" style="width: 100%;margin-top: 20px;"></div>

        <div style="width: 100%;display: flex;flex-direction: row;margin-top: 15px;">
            <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                <img src="../../../assets/imagesnew/home_tip1.png" alt="" style="width: 30px;width: 30px;">
                <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">安全合规</span>
            </div>
            <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                <img src="../../../assets/imagesnew/home_tip2.png" alt="" style="width: 30px;width: 30px;">
                <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">息费透明</span>
            </div>
            <div style="display: flex;flex-direction: column;align-items: center;flex: 1;">
                <img src="../../../assets/imagesnew/home_tip3.png" alt="" style="width: 30px;width: 30px;">
                <span style="font-size: 12px;margin-top: 10px;" class="nut-font-color-home-amount">期限灵活</span>
            </div>
        </div>
        
        <div style="width: 100%;" class="nut-flex-v-center">
            <span style="font-size: 12px;color: white;"></span>
            <span style="font-size: 15px;color: white;font-weight: bold;margin-top: 5px;"></span>
        </div>

    </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref, reactive, watch } from "vue";

import constant from '../../../utils/constant';

export default {
    props: {
        data: Object,
    },
    setup(props) {
        const router = useRouter();

        const loading = ref(false)

        return {
            loading
        };
    },

    mounted() {

    },
}
</script>
<style lang="less" scoped>
// @import "../../../styles/common.less";
</style>
