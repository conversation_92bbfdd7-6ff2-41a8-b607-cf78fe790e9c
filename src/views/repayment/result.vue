<template>
  <div class="contentBox">
    <img :src="icon" :class="(payStatus == 0 || payStatus == 1) ? 'iconRotate' : ''" style="width: 65px;height: 65px;margin-top: 40px;" />
    <p style="margin: 40px 20px 20px;color: #000;font-size: 15px;text-align: center">{{  payTips }}</p>
    <nut-button  class="viewBtn" type="primary" @click="onClickAction">查看订单</nut-button>
  </div>
</template>

<script>
import { useRouter } from 'vue-router';
import { ref } from "vue";
// import { queryPayStatus } from '../../api'
import { showToast } from "@nutui/nutui";

export default {
  setup(){
    const router = useRouter();

    const payStatus = ref(0)
    const key = ref('')

    const payTips = ref('')
    const icon = ref()

    const fetchPayStatus = async () => {
      // const res = await queryPayStatus(key.value)
      // if(res.code == 0){
      //   payStatus.value = res.data.payStatus
      //   if(payStatus.value == 2){
      //     payTips.value = '支付成功！'
      //     icon.value = getImageUrl('../../assets/imagesnew/card-sel.png')
      //   }else if(payStatus.value == 3){
      //     payTips.value = '支付失败,' + res.data.errorMsg + ',请检查银行卡后重新支付'
      //     icon.value = getImageUrl('../../assets/imagesnew/failed.png')
      //   }else{
      //     payTips.value = '订单支付中,请耐心等待' 
      //     icon.value = getImageUrl('../../assets/imagesnew/paying.png')
      //     setTimeout(() => {
      //       fetchPayStatus()
      //     }, 2000)
      //   }
      // }else{
      //   showToast.text(res.msg)
      // }
    }

    const onClickAction = () => {
      if (window.android) {
        if (window.android.jsGoBack) {
          window.android.jsGoBack();
        }
      } else {
          setupWebViewJavascriptBridge(function (bridge) {
            bridge.callHandler('jsGoBack');
          });
      }
    }

    const setupWebViewJavascriptBridge = (callback) => {
      if (window.WebViewJavascriptBridge) { return callback(WebViewJavascriptBridge); }
      if (window.WVJBCallbacks) { return window.WVJBCallbacks.push(callback); }
      window.WVJBCallbacks = [callback];
      var WVJBIframe = document.createElement('iframe');
      WVJBIframe.style.display = 'none';
      WVJBIframe.src = 'wvjbscheme://__BRIDGE_LOADED__';
      document.documentElement.appendChild(WVJBIframe);
      setTimeout(function () { document.documentElement.removeChild(WVJBIframe) }, 10);
    }

    const goBack = () => {
      router.back();
    }

    const getImageUrl = (name) => {
      return new URL(name, import.meta.url).href;
    }

    return {
      payStatus,
      key,
      payTips,
      icon,

      goBack,
      getImageUrl,
      onClickAction,
      fetchPayStatus,
    };
  },

  mounted() {
    console.log("参数----", this.$route.query);
    if (this.$route.query.key) {
      this.key = this.$route.query.key;
    }
    this.fetchPayStatus();
  },
}
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";


.contentBox{
  display: flex;
  flex-direction: column;
  align-items: center;
}

.viewBtn{
  background-color: #53AA4B;
  width: 70%;
  border: none;
}

.iconRotate{
  -webkit-transform: rotate(360deg);
  animation: rotation 2s linear infinite;
  -moz-animation: rotation 2s linear infinite;
  -webkit-animation: rotation 2s linear infinite;
  -o-animation: rotation 2s linear infinite;
}

@-webkit-keyframes rotation{
  from {-webkit-transform: rotate(0deg);}
  to {-webkit-transform: rotate(360deg);}
}

</style>