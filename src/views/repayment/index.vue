<template>
  <div>
    <nut-navbar title="待还账单"></nut-navbar>

    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

    <div style="width: 100%;height: calc(100vh - 60px);box-sizing: border-box;">
      <nut-pull-refresh v-model="refreshing" @refresh="onRefresh()" style="overflow-y: scroll;">

        <div style="width: 100%;height: calc(100vh - 60px);padding: 0 10px;box-sizing: border-box;margin-top: 10px;">

          <div :key="dataItem.tradeNo" v-for="(dataItem, dataIndex) in repaymentBillList"
            style="width: 100%;box-sizing: border-box;" class="nut-flex-v">

            <div
              style="width: 100%;border-radius: 10px;background-color: white;margin-bottom: 10px;padding: 10px;box-sizing: border-box;position: relative;">
              <div style="width: 100%;justify-content: space-between;" class="nut-flex-h-center">
                <div v-on:click.stop="copy(dataItem.tradeNo)" class="nut-flex-h-center">
                  <img src="../../assets/imagesnew/repay_bill.png" style="width: 25px;height: 25px;" alt="">
                  <span style="font-size: 14px;color: black;font-weight: 900;margin-left: 5px;">本息账单剩余待还{{
                    dataItem.needPayCapital }}</span>
                </div>
              </div>

              <div style="width: 100%;margin-top: 10px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #5EBBAF;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单期数</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.currentPeriodDesc }}</span>

                <div style="flex: 1;justify-content: flex-end" class="nut-flex-h">
                  <span style="color: black;font-size: 12px;">{{ dataItem.repaymentDate }}</span>
                </div>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #FF9900;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单周期</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.cycleDate }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #1D9FC8;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单应还本息</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.repaymentCapital }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #983BCA;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">{{ dataItem.overdueDays > 0
                  ? '已逾期' : '距离到期日' }}</span>

                <span style="font-size: 12px;margin-left: 5px"
                  :style="{ color: dataItem.overdueDays > 0 ? '#F02F33' : '#888888' }">{{ dataItem.overdueDays > 0 ?
                    dataItem.overdueDays : dataItem.repaymentDays }}</span>
              </div>

              <div
                style="width: 100%;display: flex;flex-direction: row;align-items: flex-end;justify-content: flex-end;">
                <div v-on:click.stop="clickRepayment(dataItem.billNo)" style="min-width: 100px;height: 25px;"
                  class="nut-flex-center"
                  :class="dataItem.paidStatus == 2 ? 'nut-button-enable-false' : dataItem.overdueDays > 0 ? 'nut-button-enable-overdue' : 'nut-button-enable'">
                  <span style="font-size: 14px;"
                    :style="{ color: dataItem.paidStatus == 2 ? '#CECECE' : '#FFFFFF' }">{{ dataItem.paidStatusDesc }}</span>
                </div>
              </div>

              <div style="padding: 3px 15px;"
                :class="dataItem.paidStatus == 2 ? 'nut-status-paid' : dataItem.overdueDays > 0 ? 'nut-status-overdue' : 'nut-status-not-paid'">
                <span style="font-size: 14px;" :style="{ color: dataItem.orderStatus == 2 ? '#999999' : '#FFFFFF' }">{{
                  dataItem.orderStatusDesc
                  }}</span>
              </div>
            </div>

            <div
              style="width: 100%;border-radius: 10px;background-color: white;margin-bottom: 10px;padding: 10px;box-sizing: border-box;position: relative;">
              <div style="width: 100%;justify-content: space-between;" class="nut-flex-h-center">
                <div v-on:click.stop="copy(dataItem.tradeNo)" class="nut-flex-h-center">
                  <img src="../../assets/imagesnew/repay_db.png" style="width: 25px;height: 25px;" alt="">
                  <span style="font-size: 14px;color: black;font-weight: 900;margin-left: 5px;">担保费剩余待支付{{
                    dataItem.pledgeNeedPayCapital }}</span>
                </div>
              </div>

              <div style="width: 100%;margin-top: 10px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #5EBBAF;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单期数</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.currentPeriodDesc }}</span>

                <div style="flex: 1;justify-content: flex-end" class="nut-flex-h">
                  <span style="color: black;font-size: 12px;">{{ dataItem.repaymentDate }}</span>
                </div>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #FF9900;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单周期</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.cycleDate }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #1D9FC8;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单应还本息</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.pledgeRepaymentCapital
                  }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #983BCA;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">{{ dataItem.overdueDays > 0
                  ? '已逾期' : '距离到期日' }}</span>

                <span style="font-size: 12px;margin-left: 5px"
                  :style="{ color: dataItem.overdueDays > 0 ? '#F02F33' : '#888888' }">{{ dataItem.overdueDays > 0 ?
                    dataItem.overdueDays : dataItem.repaymentDays }}</span>
              </div>

              <div
                style="width: 100%;display: flex;flex-direction: row;align-items: flex-end;justify-content: flex-end;">
                <div v-on:click.stop="clickGuarantee(dataItem.billNo)" style="min-width: 100px;height: 25px;"
                  class="nut-flex-center"
                  :class="dataItem.pledgePaidStatus == 2 ? 'nut-button-enable-false' : dataItem.overdueDays > 0 ? 'nut-button-enable-overdue' : 'nut-button-enable'">
                  <span style="font-size: 14px;" :style="{ color: '#FFFFFF' }">{{
                    dataItem.pledgePaidStatusDesc
                  }}</span>
                </div>
              </div>

              <div style="padding: 3px 15px;"
                :class="dataItem.pledgePaidStatus == 2 ? 'nut-status-paid' : dataItem.pledgeOverdueDays > 0 ? 'nut-status-overdue' : 'nut-status-not-paid'">
                <span style="font-size: 14px;" :style="{ color: '#FFFFFF' }">{{ dataItem.pledgeOrderStatusDesc
                  }}</span>
              </div>
            </div>
          </div>

          <div v-if="repaymentBillList.length == 0" style="width: 100%;" class="nut-flex-v-center">
            <img src="../../assets/imagesnew/empty.png" style="width: 150px;height: 150px;margin-top: 60px;" alt="">
            <span style="font-size: 12px;color: #999999;">暂无待还账单</span>
          </div>

          <div style="width: 100%;height: 30px;"></div>
        </div>
      </nut-pull-refresh>

    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "@nutui/nutui";

import { orderBillRepaymentList, orderBillPaidList, payClientCharges } from '../../api/repayment'
import { userHome } from '../../api/main'

import { orderBorrowAgainConfirm } from '../../api/main'
import constant from '../../utils/constant';
import JumpHandler from '../../utils/jumpHandler'

export default {
  setup() {
    const router = useRouter();

    const loading = ref(false)
    const refreshing = ref(false)

    const repaymentBillList = ref([])

    const onRefresh = () => {
      onLoad()
    }

    const onLoad = async () => {
      loading.value = true
      const res = await orderBillRepaymentList()

      loading.value = false
      refreshing.value = false

      if (res.code == 0) {
        if (res.data && res.data.repaymentBillList) {
          repaymentBillList.value = res.data.repaymentBillList
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg)
        }
      }
    }

    const clickGuarantee = (billNo) => {
      const data = {
        billNo: billNo,
        payType: 11
      }
      payClientChargeLogic(data)
    }

    const clickRepayment = (billNo) => {
      const data = {
        billNo: billNo,
        payType: 10
      }
      payClientChargeLogic(data)
    }

    const payClientChargeLogic = async (data) => {
      loading.value = true
      const res = await payClientCharges(data)
      loading.value = false
      if (res.code == 0) {
        if (res.data && res.data.payClientType) {
          if (res.data.payClientType == 5 && res.data.repaymentJson) {
            const repaymentData = JSON.parse(res.data.repaymentJson)
            if (repaymentData.payUrl) {
              const url = repaymentData.payUrl + '?url=' + encodeURIComponent(location.href)
              window.location.href = url
            }
          }
          if (res.data.payClientType == 7 && res.data.repaymentJson) {
            const repaymentData = JSON.parse(res.data.repaymentJson)
            if (repaymentData.payKey) {

              router.push({
                path: '/payapp',
                query: {
                  payKey: repaymentData.payKey,
                  orderType:data.payType == 10?1:2,
                  billNo:data.billNo
                }
              })
            }
          }
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg)
        }
      }
    }

    return {
      loading,
      refreshing,
      onRefresh,
      clickGuarantee,
      clickRepayment,
      payClientChargeLogic,
      repaymentBillList,
      onLoad,
    }
  },

  mounted() {
    this.onLoad()
  },

  components: {
    Loading
  }
}
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";

.nut-button-enable {
  border-radius: 30px;
  background-color: #02B3FE;
}

.nut-button-enable-overdue {
  border-radius: 30px;
  background-color: #F02F33;
}

.nut-button-enable-false {
  border-radius: 30px;
  background-color: #EAEAEA;
}

.nut-status-paid {
  border-bottom-left-radius: 20px;
  border-top-right-radius: 10px;
  background-color: #EAEAEA;
  position: absolute;
  right: 0;
  top: 0
}

.nut-status-not-paid {
  border-bottom-left-radius: 20px;
  border-top-right-radius: 10px;
  background-color: #02B3FE;
  position: absolute;
  right: 0;
  top: 0
}

.nut-status-overdue {
  border-bottom-left-radius: 20px;
  border-top-right-radius: 10px;
  background-color: #F02F33;
  position: absolute;
  right: 0;
  top: 0
}
</style>