<template>
    <div style="position: relative;">

        <nut-navbar title="借款信息确认" left-show @click-back="goBack"></nut-navbar>

        <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

        <div style="width: 100%;" class="nut-flex-v">
            <div style="width: 100%;box-sizing: border-box;" class="nut-flex-v">

                <div style="width: 100%;background-color: white;padding: 5px 15px;box-sizing: border-box;">
                    <div v-for="(item, index) in topTradeFieldDetails" :key="item.borrowPurpose" style="width: 100%;"
                        class="nut-flex-v">
                        <div style="width: 100%;justify-content: space-between;margin: 7px 0px;"
                            class="nut-flex-h-center">
                            <span style="font-size: 14px;color: #999999;">{{ item.title }}</span>
                            <span style="font-size: 14px;color: black;">{{ item.value }}</span>
                        </div>
                    </div>
                </div>

                <div
                    style="width: 100%;background-color: white;padding: 5px 15px;box-sizing: border-box;margin-top: 10px;">
                    <div v-for="(item, index) in middleTradeFieldDetails" :key="item.borrowPurpose" style="width: 100%;"
                        class="nut-flex-v">
                        <div style="width: 100%;justify-content: space-between;margin: 7px 0px;"
                            class="nut-flex-h-center">
                            <span style="font-size: 14px;color: #999999;">{{ item.title }}</span>
                            <span style="font-size: 14px;color: black;">{{ item.value }}</span>
                        </div>
                    </div>
                </div>

                <div
                    style="width: 100%;background-color: white;padding: 5px 15px;box-sizing: border-box;margin-top: 10px;">
                    <div v-for="(item, index) in bottomTradeFieldDetails" :key="item.borrowPurpose" style="width: 100%;"
                        class="nut-flex-v">
                        <div style="width: 100%;justify-content: space-between;margin: 7px 0px;"
                            class="nut-flex-h-center">
                            <span style="font-size: 14px;color: #999999;">{{ item.title }}</span>
                            <span style="font-size: 14px;color: black;">{{ item.value }}</span>
                        </div>
                    </div>
                </div>

                <div style="width: 100%;padding: 0px 15px;box-sizing: border-box;margin-top: 10px;" class="nut-flex-v">
                    <span style="font-size: 13px;color: #777777;">完成以下步骤立即放款</span>

                    <div style="width: 100%;background-color: white;border-radius: 10px;margin-top: 10px;">

                        <div @click="clickItem(item)" v-for="(item, index) in orderFlowVOList" :key="index"
                            style="width: 100%;padding: 10px 10px 0px 10px;box-sizing: border-box;">
                            <div style="width: 100%;justify-content: space-between" class="nut-flex-h-center">
                                <div class="nut-flex-h-center">
                                    <img :src="item.flowLogo" style="width: 35px;height: 35px;" alt="">
                                    <span style="font-size: 14px;color: black;margin-left: 10px">{{ item.flowName
                                        }}</span>
                                </div>
                                <span style="padding: 3px 15px;border-radius: 20px;"
                                    :style="{ 'background-color': item.buttonStatus == 1 ? '#FFFFFF' : '#FEF0E3', color: item.buttonStatus == 1 ? '#999999' : '#E09C52' }">{{
                                        item.buttonName }}</span>
                            </div>
                            <div style="width: 100%;height: 1px;background-color: #F6F6F6;margin-top: 10px"></div>
                        </div>
                    </div>

                    <div style="width: 100%;padding: 0 20px;box-sizing: border-box;margin-top: 20px;">
                        <div @click="clickNextStep"
                            style="width: 100%;height: 45px;border-radius: 7px;display: flex;justify-content: center;align-items: center;"
                            class="base_submit_button_gradient">
                            <span style="font-size: 16px;" class="nut-font-color-home-amount">已完成，下一步</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <nut-overlay v-model:visible="authTipShow" :close-on-click-overlay="false">
            <div style="width: 100%;height: 100%;" class="nut-flex-center">
                <div style="width: 80%;background-color: white;border-radius: 10px;" class="nut-flex-v">
                    <div style="width: 100%;height: 45px;position: relative;" class="nut-flex-center">
                        <span style="font-size: 16px;color: black;">请确认信用担保凭证收取费用</span>
                       
                        <div @click="smsDialogCloseEvent" style="width: 45px;height: 100%;position: absolute;right: 0px;" class="nut-flex-center">
                            <img src="../../assets/imagesnew/close_grey_2.png" style="width: 17px;height: 17px;" alt="">
                        </div>
                    </div>

                    <div style="width: 100%;height: 15px;"></div>
                    <CodeInput :fields="codeLength" @change="onCodeChange" :fieldWidth="32" :fieldHeight="32" />

                    <div v-if="isCountDowning" style="min-width: 100px;margin-top: 20px;" class="nut-flex-center">
                        <nut-countdown ref="countDown" millisecond :time="60000" :auto-start="false" format="ss"
                            @end="onFinish" />
                            <span>秒后重新获取验证码</span>
                    </div>

                    <div @click="resendSms" v-if="isCountDowning == false" style="min-width: 100px;margin-top: 20px;" class="nut-flex-center">
                            <span style="color: #E09C52;font-weight: 900;">重新获取验证码</span>
                    </div>

                    <div
                        style="width: 100%;padding: 0px 30px;box-sizing: border-box;margin-bottom: 20px;margin-top: 20px;">
                        <div @click="clickSmsConfirm" class="nut_main_button">
                            <span style="font-size: 14px;font-weight: bolder;color: black;">下一步</span>
                        </div>
                    </div>

                </div>
            </div>
            <!-- <div style="width: 100%;padding: 5px;box-sizing: border-box;" class="nut-flex-v">
                <div style="width: 100%;height: 45px;border-width: 1px;border-color: #EEEEEE;display: flex;border-style: solid;border-radius: 5px;padding: 5px;box-sizing: border-box;"
                    class="nut-flex-h-center">
                    <input maxlength="6" v-model="smsCode" type="phone"
                        style="flex: 1;height: 100%;outline: none;border-width: 0px;" placeholder="请输入验证码">
                    <div style="width: 1px;height: 25px;background-color: #EEEEEE;margin: 0px 10px;"></div>

                    <div style="min-width: 100px;height: 100%;position: relative;">
                        <div v-show="!isCountDowning" style="min-width: 100px;height: 100%;" class="nut-flex-center">
                            <span style="font-size: 14px;color: #E25B19;">重新获取验证码</span>
                        </div>
                        <div v-show="isCountDowning" style="min-width: 100px;height: 100%;" class="nut-flex-center">
                             
                            <nut-countdown ref="countDown" millisecond :time="60000" :auto-start="false" format="ss"
                                @end="onFinish" />
                        </div>
                    </div>
                </div>

                <div style="width: 100%;padding: 0px 20px;box-sizing: border-box;margin-bottom: 30px;">
                    <div @click="clickSmsConfirm"
                        style="width: 100%;height: 45px;border-radius: 30px;background-color: #E25B19;margin-top: 20px"
                        class="nut-flex-center">
                        <span style="font-size: 14px;color: white;">
                            下一步
                        </span>
                    </div>
                </div>
            </div> -->
        </nut-overlay>
    </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

import { useRouter } from 'vue-router';
import { ref } from "vue";

import { showToast } from "@nutui/nutui";
import Constant from '../../utils/constant'

import CodeInput from '../../components/code-input/index.vue'

import { orderContractDetail, orderContractRequest, payBindCardRequest, payBindCardResendSms, payBindCardConfirm, getContractUrl } from '../../api/main'
import constant from '../../utils/constant';

import { showDialog } from '@nutui/nutui'

export default {
    setup() {
        const loading = ref(false)
        const router = useRouter();

        const type = ref()
        const tradeNo = ref()

        const codeLength = ref(6)

        const smsCode = ref()

        const show = ref(false)
        const smsShow = ref(false)
        const isCountDowning = ref(false)

        const authTipShow = ref(false)

        const countDown = ref(null)

        const topTradeFieldDetails = ref([])
        const middleTradeFieldDetails = ref([])
        const bottomTradeFieldDetails = ref([])
        const orderFlowVOList = ref([])

        const contractDetail = ref({})

        const pledgeDetailVO = ref({})
        const borrowPurposeList = ref([])

        const flowId = ref()

        const goBack = () => {
            router.back();
        }

        const getRrderContractDetail = async () => {
            loading.value = true
            const res = await orderContractDetail(tradeNo.value)
            loading.value = false
            if (res.code == 0) {
                contractDetail.value = res.data
                pledgeDetailVO.value = res.data.pledgeDetailVO
                orderFlowVOList.value = res.data.orderFlowVOList || []

                if (res.data.borrowPurposeList && res.data.borrowPurposeList.length > 0) {
                    var borrowPurposeListContainer = []
                    res.data.borrowPurposeList.forEach(element => {
                        borrowPurposeListContainer.push({
                            name: element.desc,
                            type: element.type
                        })
                    });

                    borrowPurposeList.value = res.data.borrowPurposeList
                }

                if (res.data.topTradeFieldDetails) {
                    topTradeFieldDetails.value = res.data.topTradeFieldDetails
                }
                if (res.data.middleTradeFieldDetails) {
                    middleTradeFieldDetails.value = res.data.middleTradeFieldDetails
                }
                if (res.data.bottomTradeFieldDetails) {
                    bottomTradeFieldDetails.value = res.data.bottomTradeFieldDetails
                }
            } else {
                if (res.msg) {
                    showToast.text(res.msg)
                }
            }
        }

        const clickItem = (item) => {
            if (item.buttonStatus == 1) {
                return
            }
            clickNextStep()
        }

        const clickNextStep = async () => {
            var patams = {
                tradeNo: tradeNo.value,
            }

            if (flowId.value) {
                resendSms()
            } else {
                loading.value = true
                const res = await orderContractRequest(patams)
                loading.value = false
                if (res.code == 0) {
                    const data = res.data
                    type.value = data.jumpUrl

                    if (data.jumpUrl == constant.JUMP_URL_PLEDGE_SIGN_CONTRACT) {
                        clickSignNow()
                    }
                    if (data.jumpUrl == constant.JUMP_URL_PLEDGE_CONFIRM_SMS) {
                        var params = {
                            orderType: constant.BIND_CARD_TYPE_GUARANTEE
                        }
                        loading.value = true
                        const res2 = await payBindCardRequest(params)
                        loading.value = false
                        if (res2.code == 0) {

                            if (res2.data.errorMsg) {
                                showDialog({
                                    title: '提示信息',
                                    content: res2.data.errorMsg,
                                    noCancelBtn: true,
                                    onCancel,
                                    onOk
                                })
                            } else {
                                flowId.value = res2.data.flowId
                                smsShow.value = true
                                isCountDowning.value = true

                                authTipShow.value = true

                                setTimeout(() => {
                                    countDown.value.start();
                                }, 500);

                                console.log("authTipShow--->"+authTipShow.value);
                                
                            }
                        } else {
                            if (res2.msg) {
                                showToast.text(res2.msg)
                            }
                        }
                    }
                } else {
                    if (res.msg) {
                        showToast.text(res.msg)
                    }
                }
            }
        }

        const clickSmsConfirm = async () => {
            var params = {
                orderType: constant.BIND_CARD_TYPE_GUARANTEE,
                flowId: flowId.value,
                smsCode: smsCode.value
            }
            loading.value = true
            const res = await payBindCardConfirm(params)
            loading.value = false
            if (res.code == 0) {
                if (res.data.errorMsg) {
                    showDialog({
                        title: '提示信息',
                        content: res.data.errorMsg,
                        noCancelBtn: true,
                        onCancel,
                        onOk
                    })
                } else {
                    type.value = constant.JUMP_URL_PLEDGE_SIGN_CONTRACT
                    smsShow.value = false
                    isCountDowning.value = false
                    authTipShow.value = false

                    getRrderContractDetail()
                    clickSignNow()
                }
            } else {
                if (res.msg) {
                    showToast.text(res.msg)
                }
            }
        }

        const onFinish = () => {
            isCountDowning.value = false
        }

        const smsDialogCloseEvent = () => {

            isCountDowning.value = false
            countDown.value.reset();

            authTipShow.value = false

            console.log("smsDialogCloseEvent--");
            
        }

        const resendSms = async () => {
            var params = {
                flowId: flowId.value,
                orderType: constant.BIND_CARD_TYPE_GUARANTEE
            }
            loading.value = true
            const res = await payBindCardResendSms(params)
            loading.value = false
            if (res.code == 0) {
                if (res.data.errorMsg) {
                    showDialog({
                        title: '提示信息',
                        content: res.data.errorMsg,
                        noCancelBtn: true,
                        onCancel,
                        onOk
                    })
                } else {
                    flowId.value = res.data.flowId
                    isCountDowning.value = true
                    authTipShow.value = true

                    setTimeout(() => {
                        countDown.value.start();
                    }, 500);
                }
            } else {
                if (res.msg) {
                    showToast.text(res.msg)
                }
            }
        }

        const clickSignNow = async () => {
            loading.value = true
            const res = await getContractUrl(tradeNo.value)
            loading.value = false
            if (res.code == 0) {
                if (res.data.errorMsg) {
                    showDialog({
                        title: '提示信息',
                        content: res.data.errorMsg,
                        noCancelBtn: true,
                        onCancel,
                        onOk
                    })
                } else {
                    if (res.data && res.data.shortUrl) {
                        window.location.href = res.data.shortUrl
                    }
                }
            } else {
                if (res.msg) {
                    showToast.text(res.msg)
                }
            }
        }


        const onCodeChange = (val) => {
            const inputCode = val
            console.log("inputCode---"+inputCode);
            if(inputCode && inputCode.length == codeLength.value){
                smsCode.value = inputCode
                clickSmsConfirm()
            }
        }

        const onCancel=()=>{

        }

        const onOk=()=>{

        }

        return {
            loading,
            goBack,
            type,
            getRrderContractDetail,
            topTradeFieldDetails,
            middleTradeFieldDetails,
            bottomTradeFieldDetails,
            pledgeDetailVO,
            contractDetail,
            borrowPurposeList,
            clickNextStep,
            show,
            tradeNo,
            smsShow,
            smsCode,
            clickSmsConfirm,
            isCountDowning,
            countDown,
            onFinish,
            smsDialogCloseEvent,
            flowId,
            resendSms,
            clickSignNow,
            orderFlowVOList,
            clickItem,
            authTipShow,


            onCodeChange,
            codeLength,
            onCancel,
            onOk
        };
    },

    mounted() {
        console.log("参数----", this.$route.query);
        if (this.$route.query) {
            this.type = this.$route.query.type
            this.tradeNo = this.$route.query.tradeNo

            console.log("参数---this.tradeNo-", this.tradeNo);
            console.log("参数---this.type-", this.type);
        }
        this.getRrderContractDetail()
    },

    components: {
        Loading,
        CodeInput
    }
}
</script>
<style lang="less" scoped>
// @import "../../styles/common.less";
</style>