<template>
  <div>
    <nut-navbar title="支付" left-show @click-back="goBack"></nut-navbar>

    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

    <TabBar />

    <div v-if="containerShow1" style="width: 100%" class="nut-flex-v-center">
      <span style="font-size: 13px; color: #888888; margin-top: 40px"
        >支付金额</span
      >
      <span style="font-size: 33px; color: #000000; font-weight: 700">{{
        (repaymentData.amount / 100).toFixed(2)
      }}</span>

      <div
        style="width: 100%; padding: 0px 15px; box-sizing: border-box"
        class="nut-flex-v"
      >
        <span style="font-size: 13px; color: #888888; margin-top: 40px"
          >支付方式</span
        >

        <div
          v-if="repaymentData && repaymentData.bankRoList"
          style="
            width: 100%;
            border-radius: 10px;
            background-color: white;
            margin-top: 10px;
          "
          class="nut-flex-v"
        >
          <div
            @click="clickBankItem(item)"
            v-for="(item, index) in repaymentData.bankRoList"
            :key="index"
            style="
              width: 100%;
              padding: 15px 15px 0px 15px;
              box-sizing: border-box;
            "
          >
            <div
              style="width: 100%; justify-content: space-between"
              class="nut-flex-h-center"
            >
              <div class="nut-flex-h-center">
                <img
                  :src="item.bankLogo"
                  style="width: 40px; height: 40px"
                  alt=""
                />
                <div class="nut-flex-v" style="margin-left: 10px">
                  <span style="font-size: 15px; color: black">{{
                    item.bankName
                  }}</span>
                  <span
                    style="font-size: 12px; color: #777777; margin-top: 5px"
                    >{{ item.cardNo }}</span
                  >
                </div>
              </div>
              <img
                :src="item.check ? imgCheckYes : imgCheckNot"
                style="width: 17px; height: 17px"
                alt=""
              />
            </div>
            <div
              style="
                width: 100%;
                padding: 0px 0px 0px 50px;
                box-sizing: border-box;
                margin-top: 15px;
              "
            >
              <div
                style="width: 100%; height: 1px; background-color: #f6f6f6"
              ></div>
            </div>
          </div>
        </div>

        <div style="width: 100%; box-sizing: border-box; margin-top: 20px">
          <div
            @click="clickNextStep"
            style="
              width: 100%;
              height: 45px;
              border-radius: 7px;
              display: flex;
              justify-content: center;
              align-items: center;
            "
            class="base_submit_button_gradient"
          >
            <span style="font-size: 16px" class="nut-font-color-home-amount"
              >确认支付</span
            >
          </div>
        </div>
      </div>
    </div>

    <div v-if="containerShow2" style="width: 100%" class="nut-flex-v-center">
      <img
        src="../../assets/imagesnew/pay_loading.gif"
        style="width: 45px; height: 45px; margin-top: 50px"
        alt=""
      />
      <span style="font-size: 13px; color: #888888; margin-top: 20px"
        >支付中，正在获取支付结果，请稍等...</span
      >
    </div>

    <div v-if="containerShow3" style="width: 100%" class="nut-flex-v-center">
      <img
        :src="payStatus == 2 ? imgSuccess : imgFaild"
        style="width: 75px; height: 75px; margin-top: 50px"
        alt=""
      />
      <span
        style="font-size: 14px; margin-top: 20px"
        :style="{ color: payStatus == 2 ? '#1A8F0B' : '#F02F33' }"
        >{{ payStatusContent }}</span
      >

      <div
        style="
          width: 100%;
          padding: 0 20px;
          box-sizing: border-box;
          margin-top: 20px;
        "
      >
        <div
          @click="clickBack"
          style="
            width: 100%;
            height: 45px;
            border-radius: 7px;
            display: flex;
            justify-content: center;
            align-items: center;
          "
          class="base_submit_button_gradient"
        >
          <span style="font-size: 16px" class="nut-font-color-home-amount"
            >返回</span
          >
        </div>
      </div>
    </div>

    <repeat-card
      ref="repeatCardRef"
      v-model:flowId="flowId"
      :bindId="bindId"
      :billNo="billNo"
      v-if="orderType == 2"
    ></repeat-card>
  </div>
</template>

<script>
import Loading from "vue-loading-overlay";
import "vue-loading-overlay/dist/css/index.css";

import { useRouter } from "vue-router";
import { ref, onBeforeUnmount, nextTick } from "vue";
import { showToast } from "@nutui/nutui";
import TabBar from "../../components/Tabbar/index.vue";

import {
  cashierInfo,
  cashierDoPay,
  cashierStatus,
  checkRepeatCard,
} from "../../api/repayment";

import imgCheckNot from "../../assets/imagesnew/check_not_grey2.png";
import imgCheckYes from "../../assets/imagesnew/check_yes2.png";

import imgSuccess from "../../assets/imagesnew/success.png";
import imgFaild from "../../assets/imagesnew/failed.png";
import RepeatCard from "./repeatCard.vue";

export default {
  setup() {
    const router = useRouter();
    const flowId = ref("");
    const loading = ref(false);
    const payKey = ref("");
    const billNo = ref("");
    const repaymentData = ref({});

    const containerShow1 = ref(false);
    const containerShow2 = ref(false);
    const containerShow3 = ref(false);

    const bankRoList = ref([]);

    const bindId = ref("");

    const orderType = ref(1);

    const payStatus = ref(0);
    const payStatusContent = ref("");
    const repeatCardRef = ref();
    let timer = null;
    let canSearch = false;

    const cashierInfoGet = async (payKeyData) => {
      payKey.value = payKeyData;

      loading.value = true;
      const res = await cashierInfo(payKeyData);

      if (res.code == 0) {
        if (res.data) {
          containerShow1.value = true;
          repaymentData.value = res.data;

          if (res.data.bankRoList && res.data.bankRoList.length) {
            var bankRoListData = repaymentData.value.bankRoList;
            bankRoListData[0].check = true;
            bindId.value = bankRoListData[0].bindId;

            bankRoList.value = bankRoListData;
          }
          if (orderType.value == 2) {
            const check = await checkRepeatCard(bindId.value);
            if (check.data != null) {
              if (check.data.flowId) {
                flowId.value = check.data.flowId;
                await nextTick();
                repeatCardRef.value.open();
              }
            }
          }
        }
        loading.value = false;
      } else {
        if (res.msg) {
          showToast.text(res.msg);
        }
      }
    };

    const goBack = () => {
      router.back();
    };

    const clickNextStep = async () => {
      var params = {
        orderType: repaymentData.value.orderType,
        bindId: bindId.value,
        tradeType: "BINDCARD",
      };
      loading.value = true;

      const res = await cashierDoPay(payKey.value, params);

      loading.value = false;

      if (res.code == 0) {
        if (res.data.errorMsg) {
          showToast.text(res.data.errorMsg);
        } else {
          containerShow1.value = false;
          containerShow2.value = true;

          countDown();
        }
      } else {
        if (res.code == 400049) {
          router.push({
            path: "card-info",
            query: {
              orderType: 2,
            },
          });
        }
        if (res.msg) {
          showToast.text(res.msg);
        }
      }
    };

    const cashierStatusGet = async () => {
      const res = await cashierStatus(payKey.value);
      canSearch = true;
      if (res.code == 0) {
        if (res.data) {
          payStatus.value = res.data.payStatus;

          if (payStatus.value == 2) {
            containerShow1.value = false;
            containerShow2.value = false;
            containerShow3.value = true;
            payStatusContent.value = "支付成功，正在为您跳转...";
            canSearch = false;
            clearInterval(timer);
            timer = null;

            setTimeout(() => {
              router.back();
            }, 1000);
          }
          if (payStatus.value == 3) {
            containerShow1.value = false;
            containerShow2.value = false;
            containerShow3.value = true;
            payStatusContent.value = res.data.errorMsg;
            canSearch = false;
            clearInterval(timer);
            timer = null;
          }
        }
      } else {
        if (res.code == 400049) {
          router.push({
            path: "card-info",
            query: {
              orderType: 2,
            },
          });
        }
      }
    };

    const clickBack = () => {
      router.back();
    };

    const clickBankItem = (item) => {
      bindId.value = item.bindId;
    };

    const countDown = () => {
      timer && clearInterval(timer);
      canSearch = true;
      timer = setInterval(() => {
        console.log("countDown-->");
        if (canSearch) {
          cashierStatusGet();
        }
      }, 2000);
    };

    onBeforeUnmount(() => {
      clearInterval(timer);
      timer = null;
    });

    return {
      loading,
      repaymentData,

      imgCheckNot,
      imgCheckYes,

      containerShow1,
      containerShow2,
      containerShow3,
      billNo,
      bindId,
      orderType,
      payStatus,
      flowId,
      imgSuccess,
      imgFaild,
      payStatusContent,
      repeatCardRef,
      cashierInfoGet,
      goBack,
      clickNextStep,
      clickBankItem,
      countDown,
      clickBack,
    };
  },

  mounted() {
    if (this.$route.query.payKey) {
      const payKey = this.$route.query.payKey;
      this.cashierInfoGet(payKey);
    }
    if (this.$route.query.orderType) {
      this.orderType = this.$route.query.orderType;
      this.billNo = this.$route.query.billNo;
    }
  },

  components: {
    TabBar,
    Loading,
    RepeatCard,
  },
};
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";

.nut-button-enable {
  border-width: 1px;
  border-radius: 30px;
  border-style: solid;
  border-color: #02b3fe;
}

.nut-button-enable-false {
  border-radius: 30px;
  background-color: #eaeaea;
}
</style>
