<template>
  <div>
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />
    <nut-navbar :title="title" left-show @click-back="goBack" :fixed="true" v-if="!noTitle" />

    <div v-html="htmlStr" style="padding: 10px; padding-top: 40px"></div>
  </div>
</template>

<script>
import { useRouter } from "vue-router";
import { ref } from "vue";
import { queryProtocolContent } from "../../api";
import { showToast } from "@nutui/nutui";
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

export default {
  setup() {
    const router = useRouter();

    const pid = ref("");
    const title = ref("");
    const loading = ref(false);
    const noTitle = ref(false)

    const htmlStr = ref("");
    const appName = ref("");
    const companyName = ref("");

    const fetchProtocol = async () => {
      loading.value = true;
      const res = await queryProtocolContent(pid.value);
      loading.value = false;

      if (res.code == 0) {
        let htmlText = res.data.text;
        title.value = res.data.title;

        appName.value = res.data.appName;
        companyName.value = res.data.companyName;

        htmlText = htmlText.replaceAll("{{ appName }}", appName.value);

        htmlStr.value = htmlText;
      } else {
        showToast.text(res.msg);
      }
    };

    const goBack = () => {
      router.back();
    };

    return {
      pid,
      title,
      loading,
      htmlStr,
      appName,
      companyName,
      fetchProtocol,
      goBack,
      noTitle
    };
  },

  mounted() {
    console.log("参数----", this.$route.query);
    if (this.$route.query.pid) {
      this.pid = this.$route.query.pid;
      this.title = this.$route.query.title;

      this.fetchProtocol();
    }

    if (this.$route.query.title === 'NO') {
      //不需要头部
      this.noTitle = true
    }
  },



  components: {
    Loading
  }
};
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";</style>