<template>

  <div style="position: relative;">
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

    <img src="../../assets/imagesnew/mine_bg.png" alt="" style="width: 100%;height: 100vh;position: absolute;">

    <div style="width: 100%;position: absolute;">
      <div style="width: 100%;padding:20px;box-sizing: border-box;">

        <div style="width: 100%;justify-content: space-between;" class="nut-flex-h-center">

          <div class="nut-flex-h-center" style="width: 100%;">
            <img src="../../assets/imagesnew/avatar.png" alt="" style="width: 60px;height: 60px;">
            <div class="nut-flex-v" style="margin-left: 10px;">
              <span style="font-size: 16px;color: black;font-weight: bolder;">{{ userData.realName }}</span>
            </div>

            <div style="height: 40px;flex: 1;justify-content: flex-end;" class="nut-flex-h-center">
              <img @click="clickCustomerService" src="../../assets//imagesnew/customer_service.png" alt="" style="width: 20px;height: 20px;">
            </div>
          </div>
        </div>

        <div
          style="width: 100%;padding: 10px;margin-top: 20px;background-color: #232733;border-radius: 10px;box-sizing: border-box;justify-content: space-between;"
          class="nut-flex-h-center">
          <div class="nut-flex-v">
            <span style="font-size: 12px;color: #8B8C9D;">预估可借</span>
            <span style="font-size: 25px;color: white;font-weight: bolder;margin-top: 5px;">{{ maxAmount }}</span>
            <span style="font-size: 12px;color: #8B8C9D;margin-top: 5px;">只需3步轻松借款</span>
          </div>
          <span @click="clickGetAmount"
            style="font-size: 13px;color: black;background: linear-gradient(to right,#FDDC01,#FFC204);padding: 8px 20px;border-radius: 6px;">查看额度</span>
        </div>

        <div style="width: 100%;border-radius: 10px;background-color: white;padding: 10px 0px;margin-top: 10px"
          class="nut-flex-h-center">
          <div @click="clickRepayment" style="flex: 1;" class="nut-flex-v-center">
            <img src="../../assets/imagesnew/mine_1.png" alt="" style="width: 30px;height: 30px;">
            <span style="font-size: 14px;color: black;margin-top: 5px;">待还账单</span>
          </div>
          <div @click="clickPaidList" style="flex: 1;" class="nut-flex-v-center">
            <img src="../../assets/imagesnew/mine_2.png" alt="" style="width: 30px;height: 30px;">
            <span style="font-size: 14px;color: black;margin-top: 5px;">已还账单</span>
          </div>
          <div @click="clickBank" style="flex: 1;" class="nut-flex-v-center">
            <img src="../../assets/imagesnew/mine_3.png" alt="" style="width: 30px;height: 30px;">
            <span style="font-size: 14px;color: black;margin-top: 5px;">银行卡</span>
          </div>
          <div @click="clickSetting" style="flex: 1;" class="nut-flex-v-center">
            <img src="../../assets/imagesnew/mine_4.png" alt="" style="width: 30px;height: 30px;">
            <span style="font-size: 14px;color: black;margin-top: 5px;">设置</span>
          </div>
        </div>

        <nut-swiper :is-preventDefault="false" :init-page="0" :auto-play="3000" pagination-visible
          pagination-color="#F6A95D" :height="80" pagination-unselected-color="#FFFFFF"
          style="border-radius: 7px;margin-top: 10px;"
          :style="{ display: slideshowVOList && slideshowVOList.length > 0 ? 'flex' : 'none' }">
          <nut-swiper-item v-for="(item, index) in slideshowVOList" :key="index" style="height: 80px">
            <img @click="clickBanner(item)" :src="item.imgUrl" alt="" style="height: 100%; width: 100%;"
              draggable="false" />
          </nut-swiper-item>
        </nut-swiper>

      </div>
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';
import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "@nutui/nutui";
import JumpHandler from '../../utils/jumpHandler'

import { userPersonalCenter, orderBorrowAgainConfirm,getCustomerServiceUrl } from '../../api/main'
import { queryCheckRealName } from '@/api/common'


export default {
  setup() {

    const router = useRouter();
    const loading = ref(false);
    const userData = ref({})

    const maxAmount = ref('0')

    const slideshowVOList = ref([])
    const serviceUrl = ref()

    const clickBank = async () => {
      loading.value = true
      const res = await queryCheckRealName();
      loading.value = false

      if (res.code == 0) {
        const canBindBankCard = res.data.canBindBankCard
        if (canBindBankCard) {
          router.push('/my-cards')
        } else {
          JumpHandler.verifyJumpHandler()
        }
      } else {
        showToast.text(res.msg)
      }
    }
    const clickSetting = () => {
      router.push('/setting')
    }

    const getUserPersonalData = async () => {
      loading.value = true
      const res = await userPersonalCenter()
      loading.value = false
      if (res.code == 0) {
        const data = res.data
        if (data) {
          userData.value = data
          maxAmount.value = data.maxAmount
          slideshowVOList.value = data.slideshowVOList || []
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg)
        }
      }
    }

    const getServiceUrl=async()=>{
      const res = await getCustomerServiceUrl()
      if (res.code == 0) {
        const data = res.data
        if (data) {
          serviceUrl.value = data.customerServiceUrl
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg)
        }
      }
    }

    const clickGetAmount = async () => {
      loading.value = true
      const res = await orderBorrowAgainConfirm()
      loading.value = false
      if (res.code == 0) {
        const data = res.data
        if (data.jumpUrl) {
          JumpHandler.verifyJumpHandler()
        } else {
          router.push("/")
        }
      } else {
        if (res.msg) {
          showToast.text(res.msg)
        }
      }
    }

    const clickPaidList = () => {
      router.push('/paidlist')
    }


    const clickBanner = (data) => {
      console.log("data.jumpUrl-->" + data.jumpUrl);
      if (data.jumpUrl) {
        window.location.href = data.jumpUrl
      }
    }

    const clickRepayment=()=>{
      router.push('/repayment')
    }

    const clickCustomerService=()=>{
      if(serviceUrl.value){
        window.location.href = serviceUrl.value
      }
    }
    return {
      loading,
      clickBank,
      clickSetting,
      getUserPersonalData,
      userData,
      maxAmount,
      clickGetAmount,
      clickPaidList,
      clickBanner,
      slideshowVOList,
      clickRepayment,
      getServiceUrl,
      clickCustomerService
    }
  },

  mounted() {
    this.getUserPersonalData()
    this.getServiceUrl()
  },

  components: {
    Loading
  }
}
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";
</style>