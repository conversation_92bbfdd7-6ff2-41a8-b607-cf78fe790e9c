<template>
  <div style="padding-top: var(--van-nav-bar-height);width: 100%;position: relative;">

    <div style="width: 100%;height: 50px;position: fixed;top: 0px;background-color: white;">

      <div style="width: 100%;height: 100%;position: absolute;" class="nut-flex-center">
        <span>我的银行卡</span>
      </div>

      <div @click="jumpBindCard" style="height: 100%;position: absolute;right: 10px;" class="nut-flex-center">
        <span
          style="font-size: 12px;color: #FF8E1D;border-width: 1px;border-color: #FF8E1D;border: 1px solid #FF8E1D;border-radius: 3px;padding: 2px 10px;">重新绑定</span>
      </div>

      <div @click="goBack" style="width: 50px;height: 50px;position: absolute;" class="nut-flex-center">
        <img src="../../assets/imagesnew/arrow_left_black.png" style="width: 20px;height: 20px;" alt="">
      </div>
    </div>

    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

    <div class="nut-mycards-item nut-flex-v" v-for="(item, idx) in cardList" :key="idx" style="padding: 15px;margin-top: 60px;">

      <div class="nut-flex-h">
        <img :src="item.bankLogo" style="width: 40px;height: 40px;border-radius: 20px;" alt="">

        <div class="nut-flex-v" style="margin-left: 10px;">
          <span style="font-weight: 900;">{{ item.bankName }}</span>
          <span style="font-size: 12px;">{{ item.bankCardType }}</span>

          <span style="margin-top: 20px;">{{ item.cardNo }}</span>
        </div>
      </div>

      
    </div>
  </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

import { ref } from "vue";
import { showToast } from "@nutui/nutui";
import router from '@/router'

import { queryMyBankCards } from '../../api/common'

export default {
  setup() {

    const loading = ref(false);

    const cardList = ref([])

    const fetchCardList = async () => {
      loading.value = true
      const res = await queryMyBankCards();
      loading.value = false

      if (res.code == 0) {
        cardList.value = res.data.bankRoList || []
      } else {
        showToast.text(res.msg)
      }
    }

    const jumpBindCard = () => {
      router.push({
        path: '/card-info',
      })
    }

    const goBack = () => {
      history.back()
    }

    return {
      loading,
      cardList,

      fetchCardList,
      goBack,
      jumpBindCard,
    }
  },

  mounted() {
    this.fetchCardList()
  },

  components: {
    Loading
  }
}
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";

.nut-mycards-item {
  background: linear-gradient(to right, #5FB1FD, #4889F2);
  border-radius: 12px;
  margin: 15px 10px;
  padding: 10px;
  color: #fff;
}

</style>