<template>
    <div style="width: 100%;height: 100vh;position: relative;">
        <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />
        <nut-navbar title="设置" left-show @click-back="goBack"></nut-navbar>

        <div style="width: 100%;padding: 10px;box-sizing: border-box;position: absolute;">
            <div style="width: 100%;background-color: white;box-sizing: border-box;border-radius: 10px;"
                class="nut-flex-v">
                <div @click="jumpProtocol(zcxyPid)"
                    style="width: 100%;justify-content: space-between;padding: 15px 20px;box-sizing: border-box;"
                    class="nut-flex-h-center">
                    <div class="nut-flex-h-center">
                        <span style="font-size: 14px;color: #000;">注册协议</span>
                    </div>
                    <img src="../../assets/imagesnew/arrow_right.png" alt="" style="width: 12px;height: 12px;">
                </div>
                <div style="width: 100%;padding: 0px 20px;box-sizing: border-box;">
                    <div style="width: 100%;height: 1px;background-color: #F6F6F6;"></div>
                </div>
                <div @click="jumpProtocol(ysxyPid)"
                    style="width: 100%;justify-content: space-between;padding: 10px 20px;box-sizing: border-box;"
                    class="nut-flex-h-center">
                    <div class="nut-flex-h-center">
                        <span style="font-size: 14px;color: #000;">隐私政策</span>
                    </div>
                    <img src="../../assets/imagesnew/arrow_right.png" alt="" style="width: 12px;height: 12px;">
                </div>
                <div style="width: 100%;padding: 0px 20px;box-sizing: border-box;">
                    <div style="width: 100%;height: 1px;background-color: #F6F6F6;"></div>
                </div>
                <div @click="clickAbout"
                    style="width: 100%;justify-content: space-between;padding: 10px 20px;box-sizing: border-box;"
                    class="nut-flex-h-center">
                    <div class="nut-flex-h-center">
                        <span style="font-size: 14px;color: #000;">关于{{ appName }}</span>
                    </div>
                    <img src="../../assets/imagesnew/arrow_right.png" alt="" style="width: 12px;height: 12px;">
                </div>
                <div style="width: 100%;padding: 0px 20px;box-sizing: border-box;">
                    <div style="width: 100%;height: 1px;background-color: #F6F6F6;"></div>
                </div>

                <div style="width: 100%;justify-content: space-between;padding: 10px 20px;box-sizing: border-box;"
                    class="nut-flex-h-center">
                    <div class="nut-flex-h-center">
                        <span style="font-size: 14px;color: #000;">App版本号 v1.0.0</span>
                    </div>
                </div>
            </div>
        </div>

        <div style="width: 100%;padding: 0px 30px;box-sizing: border-box;position: absolute;bottom:40px">
            <div @click="clickSignOut" class="nut_main_button">
                <span style="font-size: 14px;font-weight: bolder;color: black;">退出登录</span>
            </div>
        </div>
    </div>
</template>

<script>
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

import { showDialog } from '@nutui/nutui'

import { useRouter } from 'vue-router';
import { ref } from "vue";
import { showToast } from "@nutui/nutui";
import Constant from '../../utils/constant'

import { userLogout } from '../../api/common'
import { userPersonalCenter } from '../../api/main'
import { logout } from '../../utils/data'

export default {
    setup() {

        const loading = ref(false)
        const router = useRouter();
        const appName = ref(import.meta.env.VITE_APP_NAME)

        const aboutUrl = ref()

        const goBack = () => {
            router.back();
        }

        const getUserPersonalData = async () => {
            loading.value = true
            const res = await userPersonalCenter()
            loading.value = false
            if (res.code == 0) {
                const data = res.data
                if (data) {
                    aboutUrl.value = data.aboutUrl
                }
            } else {
                if (res.msg) {
                    showToast.text(res.msg)
                }
            }
        }

        const onCancel = () => {

        }

        const onOk = async () => {
            loading.value = true
            const res = await userLogout()
            loading.value = false
            if (res.code == 0) {
                logout()
                router.push("/signin")
            } else {
                if (res.msg) {
                    showToast.text(res.msg)
                }
            }
        }

        const clickSignOut = () => {
            showDialog({
                closeOnClickOverlay: false,
                title: '提示',
                content: "您确认要退出登录吗？",
                onCancel,
                onOk
            })
        }

        const jumpProtocol = (pid) => {
            const params = { pid }
            router.push({
                path: '/protocol',
                query: params
            })
        }

        const clickAbout = () => {
            if (aboutUrl.value) {
                window.location.href = aboutUrl.value
            }
        }

        return {
            loading,
            goBack,
            clickSignOut,
            jumpProtocol,
            zcxyPid: Constant.PRIVACY_POLICY_ZCXY,
            ysxyPid: Constant.PRIVACY_POLICY_YSXY,
            appName,
            clickAbout,
            getUserPersonalData,
            aboutUrl,
            onCancel,
            onOk
        };
    },

    mounted() {
        this.getUserPersonalData()
    },

    components: {
        Loading
    }
}
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";
</style>