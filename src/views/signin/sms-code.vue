<template>
  <div class="nut-container">
    <loading v-model:active="actionLoading" :can-cancel="true" :is-full-page="true" />

    <img src="../../assets/imagesnew/login_back.png" alt="" class="nut-back-img" @click="goBack">
    <h1>输入验证码</h1>
    <p style="font-size: 13px;color: #999;margin-bottom: 40px;">验证码已发送至 {{ mobile }}</p>
    <code-input :fields="codeLength"  @change="onCodeChange" />
    <span class="nut-code-btn" @click="onSendCode" :style="{'color': canSend ? '#E25B19' : '#999'}">{{ codeText }}</span>
    <span :disabled="actionLoading" class="nut_main_button" @click="loginAction">确认</span>
  </div>
</template>

<script>
import { ref, onBeforeUnmount } from 'vue'
import { querySendOtp, queryLogin } from '@/api/login.js'
import { showToast } from '@nutui/nutui';
import router from '@/router'
import CodeInput from '../../components/code-input/index.vue'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

export default {
  setup(){

    const canSend = ref(true);
    const mobile = ref('');
    const code = ref('');
    const codeText = ref('发送验证码');

    const effectTime = ref(60)
    const codeLength = ref(4)
    const actionLoading = ref(false)

    let timer = null;

    const onSendCode = async () => {
      if(!canSend.value) return;
      if(!mobile.value){
        showToast.text('请输入您的手机号码')
        return;
      }

      actionLoading.value = true;
      const res = await querySendOtp(mobile.value);
      actionLoading.value = false;
      console.log('res---', res)
      if(res.code == 0){
        canSend.value = false;
        const effectT = res.data.sendVerifyCodeSuccessVo.effectiveSeconds;
        codeText.value = effectT + 's后重新获取验证码';
        effectTime.value = effectT
        countDown();
      }else{
        showToast.text(res.msg)
      }
    }

    const countDown = () => {
      timer && clearInterval(timer)
      
      timer = setInterval(() => {
        effectTime.value--;
        codeText.value = effectTime.value + 's后重新获取验证码';
        if(effectTime.value <= 0) {
          codeText.value = '重发短信验证码';
          canSend.value = true;
          clearInterval(timer);
          timer = null;
        }
      }, 1000)
    }

    const onCodeChange = (inputCode) => {
      if(inputCode.length == codeLength.value){
        code.value = inputCode
        loginAction()
      }
    }

    const loginAction = async () => {
      if(!code.value){
        showToast.text('请输入短信验证码')
        return;
      }
      const params = {
        mobile: mobile.value,
        code: code.value
      }
      actionLoading.value = true;
      const res = await queryLogin(params);
      actionLoading.value = false;

      if(res.code == 0){
        const token = res.data.token;
        localStorage.setItem('tk', token);

        clearInterval(timer);
        timer = null;
        
        router.push('/home');
      }else{
         code.value = ''
         showToast.text(res.msg)
      }
    }

    const goBack = () => {
      router.back()
    }

   onBeforeUnmount(()=>{
      clearInterval(timer)
      timer = null
   })

    return {
      mobile,
      code,
      codeText,
      actionLoading,
      effectTime,
      canSend,
      codeLength,

      onSendCode,
      loginAction,
      goBack,
      countDown,
      onCodeChange,
    }
  },

  mounted() {
    if (this.$route.query.mobile) {
      const {mobile, effectTime, codeLength = 4 } = this.$route.query
      this.mobile  = mobile
      this.effectTime = parseInt(effectTime)
      this.codeLength = parseInt(codeLength)
      console.log('effectTime000--', this.codeLength)
      if(this.effectTime > 0){
        this.codeText = effectTime + 's后重新获取验证码';
        this.countDown();
      }
    }
  },

  components: {
    CodeInput,
    Loading,
  }
}
</script>

<style lang="less" scoped>
// @import '@/styles/common.less';

.nut-container{
  height: 100vh;
  position:relative;
  background-color: #fff;
  padding: 30px;
}

.nut-back-img{
  width: 30px;
  height: 30px;
}

.phone-field{
  border-radius: 6px;
  height: 46px;
  width: 100%;
  position: relative;
  font-weight: bold;
  font-size: 20px;
}

.nut-code-btn{
  margin: 30px auto;
  border: none;
  width: 100%;
  display: block;
  text-align: center;
  width: 100%;
  font-size: 14px;
}

</style>