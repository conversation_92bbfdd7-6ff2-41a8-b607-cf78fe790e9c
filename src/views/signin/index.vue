<template>
  <div class="nut-container">
    <loading v-model:active="sendLoading" :can-cancel="true" :is-full-page="true" />

    <img src="../../assets/imagesnew/close_grey_2.png" alt="" class="nut-close-img" @click="goBack">
    <h1>欢迎使用{{ appName }}</h1>
    <div style="height: 30px"></div>
    <p style="color: #999;font-size: 12px;margin-bottom: 10px;">手机号码</p>
    <nut-input v-model="mobile" type="number" class="nut-phone-field" placeholder="请输入您的手机号码" max-length="11"></nut-input>
    <nut-button :loading="sendLoading" class="nut_main_button" @click="onSendCode" style="margin-top: 30px">获取短信验证码</nut-button>
    <div class="nut-terms-box" style="margin-top: 15px;">
      <img :src="isAgree ? agreeImg : disagreeImg" style="width: 15px;height: 15px;" mode="aspectFit" />
      <span style="margin-left: 3px; font-size: 13px;">温馨提示:未注册{{ appName }}的手机号，登录时将自动注册，且代表您已同意
        <span style="color: blue;" @click="jumpProtocol(zcxyPid)">《用户注册协议》</span>
        <span style="color: blue;" @click="jumpProtocol(ysxyPid)">《隐私保护政策》</span>
      </span>
    </div>
  </div>
</template>

<script name="signin">
import { ref } from 'vue'
import { querySendOtp } from '@/api/login.js'
import { showToast } from '@nutui/nutui';
import router from '@/router'
import agreeImg from '../../assets/imagesnew/check_yes.png'
import disagreeImg from '../../assets/imagesnew/check_not.png'
import Constant from '../../utils/constant'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

export default {
  name: 'signin',
  setup(){
    const appName = ref(import.meta.env.VITE_APP_NAME)

    const mobile = ref('');
    const sendLoading = ref(false)
    const isAgree = ref(true)

    const onSendCode = async () => {
      if(!mobile.value){
        showToast.text('请输入手机号')
        return;
      }

      sendLoading.value = true;
      const res = await querySendOtp(mobile.value);
      sendLoading.value = false;
      if(res.code == 0){
        const effectTime = res.data.sendVerifyCodeSuccessVo.effectiveSeconds;
        const codeLength = res.data.sendVerifyCodeSuccessVo.verifyCodeLength;

        router.push({
          path: '/sms-code',
          query: { 
            mobile: mobile.value, 
            effectTime,
            codeLength,
          }
        })
      }else{
        showToast.text(res.msg)
      }
    }

    const jumpProtocol = (pid) => {
      const params = { pid }
      router.push({
        path: '/protocol',
        query: params
      })
    }

    const goBack = () => {
      router.back()
    }

    return {
      mobile,
      appName,
      sendLoading,
      isAgree,
      agreeImg,
      disagreeImg,

      zcxyPid: Constant.PRIVACY_POLICY_ZCXY,
      ysxyPid: Constant.PRIVACY_POLICY_YSXY,

      onSendCode,
      jumpProtocol,
      goBack,
    }
  },

  components: {
    Loading,
  }
}
</script>

<style lang="less" scoped>
// @import '@/styles/common.less';

.nut-container{
  height: 100vh;
  position:relative;
  background-color: #fff;
  padding: 30px;
}

.nut-close-img{
  width: 30px;
  height: 30px;
}

.nut-phone-field{
  width: 100%;
  position: relative;
  font-weight: bold;
  font-size: 20px;
  padding: 12px 10px;
}

.nut-terms-box{
  display: flex;
  flex-direction: row;
  color: #999;
}

</style>