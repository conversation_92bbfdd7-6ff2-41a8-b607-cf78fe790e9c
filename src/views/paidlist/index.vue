<template>
    <div>
        <nut-navbar title="已还账单" left-show @click-back="goBack"></nut-navbar>
  
      <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />
  
      <TabBar />
  
      <div style="width: 100%;height: calc(100vh - 60px);box-sizing: border-box;">
        <nut-pull-refresh v-model="refreshing" @refresh="onRefresh()" style="overflow-y: scroll;">
  
          <div style="width: 100%;height: calc(100vh - 60px);padding: 0 10px;box-sizing: border-box;margin-top: 10px;">
  
            <div :key="dataItem.tradeNo" v-for="(dataItem, dataIndex) in paidBillList"
            style="width: 100%;box-sizing: border-box;" class="nut-flex-v">

            <div
              style="width: 100%;border-radius: 10px;background-color: white;margin-bottom: 10px;padding: 10px;box-sizing: border-box;position: relative;">
              <div style="width: 100%;justify-content: space-between;" class="nut-flex-h-center">
                <div v-on:click.stop="copy(dataItem.tradeNo)" class="nut-flex-h-center">
                  <img src="../../assets/imagesnew/repay_bill.png" style="width: 25px;height: 25px;" alt="">
                  <span style="font-size: 14px;color: black;font-weight: 900;margin-left: 5px;">本息账单剩余待还{{
                    dataItem.needPayCapital }}</span>
                </div>
              </div>

              <div style="width: 100%;margin-top: 10px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #5EBBAF;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单期数</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.currentPeriodDesc }}</span>

                <div style="flex: 1;justify-content: flex-end" class="nut-flex-h">
                  <span style="color: black;font-size: 12px;">{{ dataItem.repaymentDate }}</span>
                </div>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #FF9900;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单周期</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.cycleDate }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #1D9FC8;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单应还本息</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.repaymentCapital }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #983BCA;"></div>
                </div>
                <span
                  style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">{{ dataItem.overdueDays > 0 ? '已逾期' : '距离到期日' }}</span>

                <span style="font-size: 12px;margin-left: 5px"
                  :style="{ color: dataItem.overdueDays > 0 ? '#F02F33' : '#888888' }">{{ dataItem.overdueDays > 0 ? dataItem.overdueDays : dataItem.repaymentDays }}</span>
              </div>

              <div
                style="width: 100%;display: flex;flex-direction: row;align-items: flex-end;justify-content: flex-end;">
                <div v-on:click.stop="clickRepayment(dataItem.billNo)" style="min-width: 100px;height: 25px;"
                  class="nut-flex-center"
                  :class="dataItem.paidStatus == 2 ? 'nut-button-enable-false' : dataItem.overdueDays>0?'nut-button-enable-overdue':'nut-button-enable'">
                  <span style="font-size: 14px;" :style="{ color: dataItem.paidStatus == 2 ? '#CECECE' : '#FFFFFF' }">{{dataItem.paidStatusDesc}}</span>
                </div>
              </div>

              <div style="padding: 3px 15px;" :class="dataItem.paidStatus == 2 ?'nut-status-paid': dataItem.overdueDays>0?'nut-status-overdue':'nut-status-not-paid'">
                <span style="font-size: 14px;"
                    :style="{ color: dataItem.orderStatus == 2 ?'#999999': '#FFFFFF' }">{{ dataItem.orderStatusDesc
                    }}</span>
              </div>
            </div>

            <div
              style="width: 100%;border-radius: 10px;background-color: white;margin-bottom: 10px;padding: 10px;box-sizing: border-box;position: relative;">
              <div style="width: 100%;justify-content: space-between;" class="nut-flex-h-center">
                <div v-on:click.stop="copy(dataItem.tradeNo)" class="nut-flex-h-center">
                  <img src="../../assets/imagesnew/repay_db.png" style="width: 25px;height: 25px;" alt="">
                  <span style="font-size: 14px;color: black;font-weight: 900;margin-left: 5px;">担保费剩余待支付{{
                    dataItem.pledgeNeedPayCapital }}</span>
                </div>
              </div>

              <div style="width: 100%;margin-top: 10px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #5EBBAF;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单期数</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.currentPeriodDesc }}</span>

                <div style="flex: 1;justify-content: flex-end" class="nut-flex-h">
                  <span style="color: black;font-size: 12px;">{{ dataItem.repaymentDate }}</span>
                </div>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #FF9900;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单周期</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.cycleDate }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #1D9FC8;"></div>
                </div>
                <span style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">账单应还本息</span>

                <span style="color: #888888;font-size: 12px;margin-left: 5px">{{ dataItem.pledgeRepaymentCapital }}</span>
              </div>
              <div style="width: 100%;margin-top: 5px;" class="nut-flex-h-center">
                <div style="width: 25px;" class="nut-flex-center">
                  <div style="width: 6px;height: 6px;border-radius: 6px;background-color: #983BCA;"></div>
                </div>
                <span
                  style="color: black;font-size: 12px;min-width: 90px;margin-left: 5px">{{ dataItem.overdueDays > 0 ? '已逾期' : '距离到期日' }}</span>

                <span style="font-size: 12px;margin-left: 5px"
                  :style="{ color: dataItem.overdueDays > 0 ? '#F02F33' : '#888888' }">{{ dataItem.overdueDays > 0 ? dataItem.overdueDays : dataItem.repaymentDays }}</span>
              </div>

              <div
                style="width: 100%;display: flex;flex-direction: row;align-items: flex-end;justify-content: flex-end;">
                <div v-on:click.stop="clickRepayment(dataItem.billNo)" style="min-width: 100px;height: 25px;"
                  class="nut-flex-center"
                  :class="dataItem.pledgePaidStatus == 2 ? 'nut-button-enable-false' : dataItem.overdueDays>0?'nut-button-enable-overdue':'nut-button-enable'">
                  <span style="font-size: 14px;" :style="{ color: '#FFFFFF'}">{{
                    dataItem.pledgePaidStatusDesc
                    }}</span>
                </div>
              </div>

              <div style="padding: 3px 15px;" :class="dataItem.pledgePaidStatus == 2 ?'nut-status-paid': dataItem.pledgeOverdueDays>0?'nut-status-overdue':'nut-status-not-paid'">
                <span style="font-size: 14px;"
                    :style="{ color: '#FFFFFF' }">{{ dataItem.pledgeOrderStatusDesc
                    }}</span>
              </div>
            </div>
          </div>
  
            <div v-if="paidBillList.length == 0" style="width: 100%;" class="nut-flex-v-center">
              <img src="../../assets/imagesnew/empty.png" style="width: 150px;height: 150px;margin-top: 60px;" alt="">
              <span style="font-size: 12px;color: #999999;">暂无已还账单</span>
            </div>
  
            <div style="width: 100%;height: 30px;"></div>
          </div>
        </nut-pull-refresh>
  
      </div>
    </div>
  </template>
  
  <script>
  import Loading from 'vue-loading-overlay';
  import 'vue-loading-overlay/dist/css/index.css';
  
  import { useRouter } from 'vue-router';
  import { ref } from "vue";
  import { showToast } from "@nutui/nutui";
  import TabBar from '../../components/Tabbar/index.vue'
  
  import { orderBillPaidList } from '../../api/repayment'
  import { userHome } from '../../api/main'
  
  export default {
    setup() {
      const router = useRouter();
  
      const loading = ref(false)
      const refreshing = ref(false)
  
      const paidBillList = ref([])
  
      const onRefresh = () => {
        onLoad()
      }
  
      const onLoad = async () => {
        loading.value = true
        const res = await orderBillPaidList()
  
        loading.value = false
        refreshing.value = false
  
        if (res.code == 0) {
          if (res.data && res.data.paidBillList) {
            paidBillList.value = res.data.paidBillList
          }
        } else {
          if (res.msg) {
            showToast.text(res.msg)
          }
        }
      }
  
      const goBack=()=>{
        router.back();
      }
  
      return {
        loading,
        refreshing,
        onRefresh,
        paidBillList,
        onLoad,
        Loading,
        goBack
      }
    },
  
    mounted() {
      this.onLoad()
    },
  
    components: {
      TabBar,
      Loading
    }
  }
  </script>
  
  <style lang="less" scoped>
  // @import "../../styles/common.less";
  
  .nut-button-enable {
    border-width: 1px;
    border-radius: 30px;
    border-style: solid;
    border-color: #02B3FE;
  }
  
  .nut-button-enable-false {
    border-radius: 30px;
    background-color: #EAEAEA;
  }
  </style>