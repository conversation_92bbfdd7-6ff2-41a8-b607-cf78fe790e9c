<template>
  <nut-navbar :title="title" left-show @click-back="goBack" :fixed="true" />
  <div style="height: 100vh;position: relative;padding-top: var(--van-nav-bar-height);" >
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />
    <div class="nut-top-tips">
      <img
        src="../../assets/imagesnew/warning.png"
        style="width: 18px; height: 18px"
        alt=""
      />
      <span style="color: #ea9947; font-size: 14px;margin-left: 6px;"
        >仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密</span
      >
    </div>

    <div class="nut-container">
      <div v-for="(item, index) in infoList" :key="index" >
        <div v-for="(single,idx) in item.inputParams" :key="single.param+idx">
          <AuthItem :ref="cellRef" :inputObject="single" />
        </div>
      </div>
    </div>
    <div class="nut-bottom-box">
      <nut-button class="nut-next-btn" style="width: 100%" @click="nextAction">已完成，下一步</nut-button>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { queryPersonalInfo, savePersonalInfo } from '@/api/profile'
import { showToast } from '@nutui/nutui';
import AuthItem from '@/components/auth-item/index.vue'
import router from '@/router'
import JumpHandler from '../../utils/jumpHandler'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

export default {
  setup(){
    const loading = ref(false);

    const itemRefs = ref([]);
    const cellRef = (e) => {
      itemRefs.value.push(e);
    }

    const title = '基本信息';
    const infoList = ref([]);

    const fetchData = async () => {
      loading.value = true;
      const res = await queryPersonalInfo();
      loading.value = false;

      if(res.code == 0){
        infoList.value = res.data.userInputInfoVOList || [];
      }else{
        showToast.text(res.msg)
      }
    }

    const nextAction = async () => {
      let params = {};
      const cellList = [];
      for(let i=0; i<infoList.value.length;i++){
        for(let m=0; m<infoList.value[i].inputParams.length;m++){
          cellList.push(1);
        }
      }

      for(let j=0;j<cellList.length;j++){
          const inputParam = itemRefs.value[j].inputParam;
          console.log('inputParam---', inputParam)
          if(!inputParam.inputValue){
            showToast.text(inputParam.inputDesc + ' ' + inputParam.paramName)
            return;
          }
          params[inputParam.param] = inputParam.inputValue;
      }

      console.log('params-----', params)
      loading.value = true;
      const res = await savePersonalInfo(params);
      loading.value = false;

      if(res.code == 0){
        showToast.text('保存成功')
        // goBack()
        JumpHandler.verifyJumpHandler()
        
      }else{
        showToast.text(res.msg);
      }
    }

    const goBack = () => {
      history.back()
    }

    return {
      title,
      infoList,
      loading,

      fetchData,
      nextAction,
      cellRef,
      goBack,
    }
  },

  mounted(){
    this.fetchData();
  },

  components: {
    Loading
  }
}
</script>

<style lang="less" scoped>
// @import '@/styles/common.less';

.nut-container{
  padding: 10px 15px 90px;
}

</style>