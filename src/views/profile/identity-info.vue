<template>
  <nut-navbar :title="title" left-show @click-back="goBack" :fixed="true" />
  <div
    style="
      padding-bottom: 120px;
      position: relative;
      padding-top: var(--nut-nav-bar-height);
      background: #f6f6f6;
      overflow-y: visible;
    "
  >
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />
    <div class="nut-top-tips">
      <img
        src="../../assets/imagesnew/warning.png"
        style="width: 18px; height: 18px"
        alt=""
      />
      <span style="color: #ea9947; font-size: 14px; margin-left: 6px"
        >仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密</span
      >
    </div>

    <div style="padding: 0 12px">
      <div class="nut-upload-box">
        <nut-uploader
          @oversize="onOversize"
          :reupload="true"
          :before-xhr-upload="(xhr, options) => beforeXHRUpload(xhr, options, 1)"
          :max-count="1"
          accept="image/*, .heic, .HEIC"
          ref="frontRef"
          name="name"
          v-model:file-list="frontFileList"
        >
          <div class="nut-bg-white nut-face-item">
            <img :src="frontUrl || frontDefaultImg" class="nut-face-img" />
            <span style="margin-top: 10px">点击拍摄人像面</span>
          </div>
        </nut-uploader>

        <nut-uploader
          @oversize="onOversize"
          :reupload="true"
          @click-upload="beforeClickUpload(2)"
          :before-xhr-upload="(xhr, options) => beforeXHRUpload(xhr, options, 2)"
          :max-count="1"
          accept="image/*, .heic, .HEIC"
          ref="backRef"
          name="name"
          v-model:file-list="backFileList"
        >
          <div class="nut-bg-white nut-face-item">
            <img :src="backUrl || backDefaultImg" class="nut-face-img" />
            <span style="margin-top: 10px">点击拍摄国徽面</span>
          </div>
        </nut-uploader>
      </div>

      <p
        style="
          color: #666;
          font-size: 14px;
          margin-bottom: 5px;
          margin-top: 40px;
        "
      >
        身份证照片拍摄说明
      </p>
      <img
        src="../../assets/imagesnew/i_center_tip.png"
        class="nut_center_img"
      />

      <h3 style="margin-bottom: 0; margin-top: 30px">身份证信息</h3>
      <p style="color: red; margin-top: 5px; font-size: 12px">
        如识别有误，请重新拍摄身份证或直接修改
      </p>

      <nut-form style="text-align: right">
        <nut-form-item label="姓名" required>
          <nut-input
            v-model="realName"
            placeholder="请输入您的姓名"
            type="text"
          />
        </nut-form-item>
        <nut-form-item label="身份证号" required>
          <nut-input
            v-model="idCard"
            placeholder="请输入您的身份证号"
            type="text"
          />
        </nut-form-item>
      </nut-form>

      <div class="nut-bottom-box">
        <nut-button
          @click="handleOCRSubmit"
          class="nut-next-btn"
          style="width: 100%"
          >已完成,下一步</nut-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import Loading from "vue-loading-overlay";
import "vue-loading-overlay/dist/css/index.css";

import { ref } from "vue";
import {
  queryFaceConfig,
  queryOCRUpload,
  queryOCRSubmit,
  queryFaceComparision,
  queryAuthList,
} from "@/api/profile";
import JumpHandler from "../../utils/jumpHandler";
import Upload from "../../utils/upload";
import Constant from "../../utils/constant";
import router from "@/router";
import { showToast } from "@nutui/nutui";

import frontDefaultImg from "../../assets/imagesnew/identiy_front.png";
import backDefaultImg from "../../assets/imagesnew/identiy_back.png";

// 1 正面  2 反面  3 人脸
export default {
  setup() {

    const title = "身份认证";
    const loading = ref(false);

    const ocrSucc = ref(false);

    const realName = ref("");
    const idCard = ref("");

    const type = ref(1);
    const frontUrl = ref("");
    const backUrl = ref("");
    const faceUrl = ref("");

    const frontRef = ref(null);
    const backRef = ref(null);
    const faceRef = ref(null);

    const frontFileList = ref([])
    const backFileList = ref([])

    const fetchFaceConfig = async () => {
      loading.value = true;
      const res = await queryFaceConfig();
      loading.value = false;

      if (res.code == 0) {
        const { data = {} } = res;
        frontUrl.value = data.faceUrl || "";
        backUrl.value = data.frontUrl || "";
        realName.value = data.realName || "";
        idCard.value = data.idCard || "";
      } else {
        showToast.text(res.msg);
      }
    };

    const beforeXHRUpload = async (xhr, options, idx) => {
      console.log("文件----", xhr, options, idx);
      loading.value = true;
      const imgUrl = await Upload.uploadAction(options.sourceFile);
      loading.value = false;
      console.log("上传结果---", imgUrl);
      type.value = idx;
      if (imgUrl) {
        switch (type.value) {
          case 1:
            handleOCRUpload(imgUrl);
            break;
          case 2:
            handleOCRUpload(imgUrl);
            break;
          case 3:
            faceUrl.value = imgUrl;
            break;
        }
      }
    };

    // 1 ocr上传识别
    const handleOCRUpload = async (imgUrl) => {
      let params = { type: 1, pictureUrl: imgUrl };
      if (type.value == 1) {
        params.pictureType = 2;
      } else if (type.value == 2) {
        params.pictureType = 1;
      }

      loading.value = true;
      const res = await queryOCRUpload(params);
      frontFileList.value = []
      backFileList.value = []
      loading.value = false;
      if (res.code == 0) {
        if (type.value == 1) {
          frontUrl.value = imgUrl;
          const idCardInfo = res.data.idCardInfo || {};
          realName.value = idCardInfo.fullName;
          idCard.value = idCardInfo.accountNumber;
        } else if (type.value == 2) {
          backUrl.value = imgUrl;
        }

        // handleOCRSubmit()
      } else {
        showToast.text(res.msg);
      }
    };

    // 2 ocr 提交
    const handleOCRSubmit = async () => {

      loading.value = true;
      const authRes = await queryAuthList()
      loading.value = false;
      if (authRes.code == 0) {
        const nextJumpUrl = authRes.data.nextJumpUrl;
        if(!nextJumpUrl){
          goBack()
          return;
        }else{
          if(nextJumpUrl == 'APP/PROFILE/PERSONAL_INFO'){
            router.replace('/basic-info')
            return
          }
        }
      }

      if (!faceComparePreCheck()) return;
      console.log("开始ocr提交---");
      loading.value = true;
      const params = {
        frontUrl: backUrl.value,
        faceUrl: frontUrl.value,
        realName: realName.value,
        idCard: idCard.value,
      };
      const res = await queryOCRSubmit(params);
      loading.value = false;

      if (res.code == 0) {
        const authUrl = res.data.authUrl;
        if (authUrl) {
          window.location.href = authUrl;
        }
      } else {
        showToast.text(res.msg);
      }
    };

    // 3 人脸比对
    const handleFaceCompare = async () => {
      if (!faceComparePreCheck()) return;

      if (!ocrSucc.value) {
        handleOCRSubmit();
        return;
      }

      loading.value = true;
      const res = await queryFaceComparision({ bestFaceUrl: faceUrl.value });
      loading.value = false;
      if (res.code == 0) {
        showToast.text("实名认证成功！");
        JumpHandler.verifyJumpHandler();
      } else {
        showToast.text(res.msg);
      }
    };

    const faceComparePreCheck = () => {
      if (!frontUrl.value) {
        showToast.text("请先拍摄身份证人像面");
        setTimeout(() => {
          frontRef.value.chooseFile();
        }, 700);
        return false;
      }
      if (!backUrl.value) {
        showToast.text("请先拍摄身份证国徽面");
        console.log("back---", backRef.value);
        setTimeout(() => {
          backRef.value.fileItemClick();
        }, 700);
        return false;
      }
      if (!realName.value) {
        showToast.text("请输入您的姓名");
        return false;
      }
      if (!idCard.value) {
        showToast.text("请输入您的身份证号");
        return false;
      }
      // if(!faceUrl.value){
      //   showToast.text('请您先进行人脸校验')
      //   setTimeout(() => { faceRef.value.chooseFile() }, 700)
      //   return false;
      // }
      return true;
    };

    const onOversize = (file) => {
      showToast.text(t("face.image_size_tip"));
    };


    const goBack = () => {
      history.back();
    };

    return {
      title,
      loading,

      ocrSucc,
      realName,
      idCard,

      type,
      frontUrl,
      backUrl,
      faceUrl,
      frontDefaultImg,
      backDefaultImg,

      faceRef,
      frontRef,
      backRef,

      frontFileList,
      backFileList,

      fetchFaceConfig,
      goBack,
      handleFaceCompare,
      beforeXHRUpload,
      onOversize,
      handleOCRSubmit,
    };
  },

  mounted() {
    this.fetchFaceConfig();
  },

  components: {
    Loading,
  },
};
</script>

<style lang="less" scoped>
// @import "@/styles/common.less";

.nut-upload-box {
  margin-top: 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  height: 140px;
}

.nut-uploader {
  width: 48%;
  border-radius: 10px;
}
:deep(.nut-uploader__slot) {
  width: 100%;
  height: 100%;
}

.nut-face-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 15px;
  border-radius: 12px;
  width: 80%;
  height: 100%;

  h3 {
    margin: 10px 0;
  }
  span {
    font-size: 13px;
    color: #666;
  }
}

.nut-face-img {
  width: 90%;
  height: 80%;
  object-fit: contain;
}

.nut_center_img {
  width: 100%;
  height: 66px;
}

:deep(input) {
  text-align: right !important;
}
</style>
