<template>
  <nut-navbar :title="title" left-show @click-back="goBack" :fixed="true" />
  <div style="height: 100vh;position: relative;padding-top: var(--nut-navbar-height);" >
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />
    <div class="nut-top-tips">
      <img
        src="../../assets/imagesnew/warning.png"
        style="width: 18px; height: 18px"
        alt=""
      />
      <span style="color: #ea9947; font-size: 14px;margin-left: 6px;"
        >仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密</span
      >
    </div>
    <div class="nut-container">
      <div v-for="(item, index) in infoList" :key="index" >
        <p style="margin-left: 10px;color: #000;font-size: 14px;">{{ item.name }}</p>
        <div class="nut-rel-container">
          <!-- <AuthItem v-if="idx<3" :ref="(el) => cellRef(el, index)" :inputObject="item" /> -->
          <h4 style="margin: 5px 0;">{{ item.inputParams[0].paramName }}</h4>
          <div class="nut-select-box">
            <span
              v-for="(relItem) in item.inputParams[0].selectVo" :key="relItem.type"
              @click="selectRelationship(relItem, index)"
              class="nut-rel-text"
              :class="item.inputParams[0].inputValue == relItem.type ? 'nut-selected-vo' : 'nut-unselected-vo'"
            >
              {{ relItem.name }}
            </span>
          </div>
          <div>

          </div>
          <nut-input class="nut-rel-input" v-model="item.inputParams[1].inputValue"  clearable input-align="right">
            <template #left>
              <span style="color: #999;">{{ item.inputParams[1].paramName }}</span>
            </template>
          </nut-input>
          <nut-input class="nut-rel-input" v-model="item.inputParams[2].inputValue"  clearable input-align="right" type="number" max-length="11">
            <template #left>
              <span style="color: #999;">{{ item.inputParams[2].paramName }}</span>
            </template>
          </nut-input>
        </div>
      </div>
    </div>
    <div class="nut-bottom-box">
      <nut-button
        @click="nextAction"
        class="nut-next-btn"
        style="width: 100%"
        >已完成,下一步</nut-button
      >
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { queryContactsInfo, saveContactsInfo } from '@/api/profile'
import { showToast } from '@nutui/nutui';
import JumpHandler from '../../utils/jumpHandler'
import Loading from 'vue-loading-overlay';
import 'vue-loading-overlay/dist/css/index.css';

export default {
  setup(){
    const loading = ref(false);

    const itemRefs = ref([[],[]]);
    const cellRef = (e, index) => {
      itemRefs.value[index].push(e);
    }

    const title = '联系人信息'
    const infoList = ref([]);

    const fetchData = async () => {
      loading.value = true;
      const res = await queryContactsInfo();
      loading.value = false;
      if(res.code == 0){
        infoList.value = res.data.userInputInfoVOs || [];
      }else{
        showToast.text(res.msg)
      }
    }

    const selectRelationship = (item, idx) => {
      infoList.value[idx].inputParams[0].inputValue = item.type
    }

    const nextAction = async () => {
      
      let params = {};
      const cellList = [];
      for(let i=0; i < infoList.value.length;i++){
        const tempList = {};
        for(let m=0; m<infoList.value[i].inputParams.length;m++){
          if(m>2) continue;
          console.log('itemRefs---', itemRefs.value, i, m)
          const inputParam = infoList.value[i].inputParams[m];
          console.log('inputParam---', inputParam)
          if(!inputParam.inputValue){
            showToast.text(inputParam.inputDesc + ' ' + inputParam.paramName)
            return;
          }
          tempList[inputParam.param] = inputParam.inputValue;
        }
        tempList.type = i;
        cellList.push(tempList);
      }

      params.contactVos = cellList
      console.log('params-----', params)
      loading.value = true;
      const res = await saveContactsInfo(params);
      loading.value = false;
      if(res.code == 0){
        showToast.text('保存成功')
        JumpHandler.verifyJumpHandler()

      }else{
        showToast.text(res.msg);
      }
    }

    const goBack = () => {
      history.back()
    }

    return {
      title,
      infoList,
      loading,

      fetchData,
      nextAction,
      cellRef,
      goBack,
      selectRelationship,
    }
  },

  mounted(){
    this.fetchData();
  },

  components: {
    Loading
  }
}
</script>

<style lang="less" scoped>
// @import '@/styles/common.less';

.nut-container{
  padding: 10px 15px 80px;
}

.nut-rel-container{
  padding: 12px 15px;
  border-radius: 10px;
  background: #fff;
  margin: 10px;
}

.nut-select-box{
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  margin: 15px 0;
}

.nut-rel-input{
  border-bottom: 1px solid #eee;
  padding: 14px  10px;
  color: #333;
  font-size: 16px;
}

.nut-rel-text{
  border-radius: 4px;
  padding: 8px 14px;
  font: 13px;
}
.nut-selected-vo{
  background: @buttonEndColor;
  color: #fff;
}

.nut-unselected-vo{
  background: #f6f6f6;
  color: #444;
}

</style>