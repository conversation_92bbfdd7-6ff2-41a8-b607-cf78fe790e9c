<template>
  <nut-navbar :title="title" left-show @click-back="goBack" :fixed="true" />
  <div style="height: 100vh; position: relative">
    <loading v-model:active="loading" :can-cancel="true" :is-full-page="true" />

    <div class="nut-top-tips">
      <img
        src="../../assets/imagesnew/warning.png"
        style="width: 18px; height: 18px"
        alt=""
      />
      <span style="color: #ea9947; font-size: 14px; margin-left: 6px"
        >仅用于身份验证、额度评估、信贷相关业务审核使用，您的信息将严格保密</span
      >
    </div>

    <div
      style="
        margin: 30px 12px;
        padding: 10px 12px;
        box-sizing: content-box;
        border-radius: 8px;
        background-color: #fff;
      "
    >
      <div>
        <p style="font-size: 18px; color: #222; margin-bottom: 0px">
          输入卡号添加
        </p>
        <p style="font-size: 13px; color: #999; margin-top: 5px">
          绑定本人一类借记卡
        </p>
      </div>
      <div class="nut-card-item-box">
        <span class="nut-card-item-title">卡号</span>
        <nut-input
          class="nut-card-input"
          v-model="bankNo"
          placeholder="请输入本人银行卡号"
          type="number"
        />
      </div>

      <div @click.prevent="showPicker = true" class="nut-card-item-box">
        <span class="nut-card-item-title" style="width: 76px">所属银行</span>
        <!-- <nut-input class="nut-card-input" v-model="bankName" placeholder="请选择银行">
          <template #right>
            <img src="../../assets/imagesnew/arrow_right.png" alt="" style=" width: 16px;height: 16px;">
          </template>
        </nut-input> -->
        <div
          class="nut-card-input"
          style="
            border-bottom: 1px solid #eaf0fb;
            flex: 1;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            padding: 14px 1px;
          "
        >
          <span v-if="bankName" style="color: #333; font-size: 14px">{{
            bankName
          }}</span>
          <span v-else style="color: #999; font-size: 14px">请选择银行</span>
          <img
            src="../../assets/imagesnew/arrow_right.png"
            alt=""
            style="width: 16px; height: 16px"
          />
        </div>
      </div>

      <div class="nut-card-item-box">
        <span class="nut-card-item-title">手机号</span>
        <nut-input
          class="nut-card-input"
          v-model="mobile"
          placeholder="请输入预留手机号"
          type="number"
          max-length="11"
        />
      </div>
      <div class="nut-card-item-box">
        <span class="nut-card-item-title">验证码</span>
        <nut-input
          class="nut-card-input"
          v-model="smsCode"
          placeholder="请输入验证码"
          type="number"
          max-length="6"
        >
          <template #right>
            <span
              :class="canSendCode ? 'nut-send-button' : 'nut-timer-button'"
              @click="onSendCode"
              style="font-size: 15px"
            >
              {{ codeText }}</span
            >
          </template>
        </nut-input>
      </div>
    </div>

    <nut-button
      @click="confirmBind"
      class="nut-next-btn"
      style="width: 80%; margin: 40px auto; display: block"
      >已完成,下一步</nut-button
    >

    <nut-popup v-model:visible="showPicker" round position="bottom">
      <nut-picker
        :columns="bankList"
        @cancel="showPicker = false"
        @confirm="selectBank"
        :field-names="customFieldName"
      />
    </nut-popup>
  </div>
</template>

<script>
import Loading from "vue-loading-overlay";
import "vue-loading-overlay/dist/css/index.css";
import JumpHandler from "../../utils/jumpHandler";
import { useRouter, useRoute } from "vue-router";
import { ref } from "vue";
import {
  querySupportBankList,
  queryBindCardRequest,
  queryBindCardResendSms,
  queryBindCardConfirm,
  queryApplyOrder,
} from "../../api";

import { userHome } from "../../api/main";
import { showToast } from "@nutui/nutui";
import Constant from "../../utils/constant";

export default {
  setup() {
    const router = useRouter();
    const route = useRoute();
    const title = "添加银行卡";

    const isFirstBind = ref(false);
    const loading = ref(false);
    const isAgree = ref(true);
    const showDialog = ref(false);
    const showPicker = ref(false);
    const bankList = ref([]);
    const customFieldName = {
      text: "bankName",
      value: "bankCode",
    };

    const bankNo = ref("");
    const bankName = ref("");
    const bankCode = ref("");
    const mobile = ref("");
    const smsCode = ref("");
    const flowId = ref("");

    const orderType = ref(1);

    const codeText = ref("发送验证码");
    const canSendCode = ref(true);
    const timer = ref(null);

    const payInfo = ref({
      token: "",
    });

    const fetchBankList = async () => {
      loading.value = true;
      const res = await querySupportBankList();
      loading.value = false;
      if (res.code == 0) {
        bankList.value = res.data.bankRoList || [];
        console.log(bankList.value);
      } else {
        showToast.text(res.msg);
      }
    };

    const selectBank = ({ selectedOptions }) => {
      console.log("selectedOptions---", selectedOptions);
      const item = selectedOptions[0];
      bankCode.value = item.bankCode;
      bankName.value = item.bankName;
      showPicker.value = false;
    };

    const onSendCode = async () => {
      if (!canSendCode.value) return;
      if (!bankNo.value) {
        showToast.text("请先输入银行卡号");
        return;
      }
      if (!bankCode.value) {
        showToast.text("请先选择所属银行");
        return;
      }
      if (!mobile.value) {
        showToast.text("请先输入预留手机号");
        return;
      }

      let params = {
        cardNo: bankNo.value,
        bankCode: bankCode.value,
        bankName: bankName.value,
        mobile: mobile.value,
        orderType: orderType.value,
      };
      // if(flowId.value){
      //   params.flowId = flowId.value
      // }

      // 发送验证码
      loading.value = true;
      let res = await queryBindCardRequest(params);
      // if(flowId.value){
      //   res = await queryBindCardResendSms(params)
      // }else{
      //   res = await queryBindCardRequest(params)
      // }
      loading.value = false;

      if (res.code == 0) {
        let errorMsg = res.data.errorMsg;
        if (errorMsg) {
          showToast.text(errorMsg);
        } else {
          flowId.value = res.data.flowId;
          getCodeState();
        }
      } else {
        showToast.text(res.msg);
      }
    };

    const maxCountSeconds = 60;
    const getCodeState = () => {
      canSendCode.value = false;
      codeText.value = maxCountSeconds + "s";
      var count = maxCountSeconds;
      timer.value = setInterval(() => {
        count--;
        codeText.value = count + "s";
        if (count <= 0) {
          clearInterval(timer.value);
          timer.value = null;
          codeText.value = "发送验证码";
          canSendCode.value = true;
        }
      }, 1000);
    };

    const confirmBind = async () => {
      if (!bankNo.value) {
        showToast.text("请先输入银行卡号");
        return;
      }
      if (!bankCode.value) {
        showToast.text("请先选择所属银行");
        return;
      }
      if (!mobile.value) {
        showToast.text("请先输入预留手机号");
        return;
      }
      if (!smsCode.value) {
        showToast.text("请先输入验证码");
        return;
      }

      loading.value = true;
      const params = {
        flowId: flowId.value,
        orderType: orderType.value,
        smsCode: smsCode.value,
      };
      const res = await queryBindCardConfirm(params);
      loading.value = false;

      if (res.code == 0) {
        let errorMsg = res.data.errorMsg;
        if (errorMsg) {
          showToast.text(errorMsg);
        } else {
          if (isFirstBind.value) {
            handleApplyOrder();
          } else {
            showToast.text("绑卡成功");
            goBack();
          }
        }
      } else {
        showToast.text(res.msg);
      }
    };

    const handleApplyOrder = async () => {
      loading.value = true;
      const res = await queryApplyOrder({ productCode: "" });
      loading.value = false;
      if (res.code == 0) {
        if (route.query.qd == "jiurongjiekuan") {
          const r = await userHome();
          if (r.code != 0) {
            router.replace("/");
            return;
          }
          router.replace({
            path: "/contract",
            query: {
              type: "APP/Product/ConfirmTrade",
              tradeNo: r.data.tradeNo,
            },
          });
          return;
        }
        router.replace("/");
      } else {
        showToast.text(res.msg);
      }
    };

    const goBack = () => {
      router.back();
    };

    const jumpProtocol = () => {
      const params = {
        pid: Constant.PRIVACY_POLICY_ZHWTKKSQS,
      };
      router.push({
        path: "/protocol",
        query: params,
      });
    };

    return {
      title,
      loading,
      showDialog,
      payInfo,
      bankNo,
      bankName,
      bankCode,
      mobile,
      smsCode,
      flowId,
      showPicker,
      customFieldName,
      bankList,
      isAgree,
      codeText,
      canSendCode,
      timer,
      isFirstBind,
      orderType,
      route,
      fetchBankList,
      selectBank,
      goBack,
      confirmBind,
      jumpProtocol,
      onSendCode,
    };
  },

  mounted() {
    console.log("参数----", this.$route.query);
    if (this.$route.query.isFirstBind) {
      this.isFirstBind = this.$route.query.isFirstBind === "true";
      console.log("是否初绑---", this.isFirstBind);
    }

    if (this.$route.query.orderType) {
      this.orderType = this.$route.query.orderType;
    }
    this.fetchBankList();
  },

  components: {
    Loading,
  },
};
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";

.nut-card-item-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

  .nut-card-item-title {
    width: 90px;
    color: #666;
    font-size: 14px;
  }
}

.nut-card-input {
  padding: 14px 10px;
  font-size: 16px;
  color: #222;
}

:deep(.nut-input--disabled input:disabled) {
  -webkit-text-fill-color: #777 !important;
}
</style>
