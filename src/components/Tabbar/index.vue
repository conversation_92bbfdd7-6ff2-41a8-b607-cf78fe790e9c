<template>
  <div
    style="
      width: 100%;
      height: 50px;
      position: fixed;
      bottom: 0px;
      background-color: white;
    "
    class="nut-flex-h-center"
  >
    <div
      @click="onTabClick(index)"
      v-for="(item, index) in tabDataList"
      :key="index"
      style="flex: 1"
      class="nut-flex-v-center"
    >
      <img
        :src="item.check ? item.icon.active : item.icon.inactive"
        style="width: 20px; height: 20px"
        alt=""
      />
      <span
        style="font-size: 13px"
        :style="{ color: item.check ? '#000000' : '#8A8A8A' }"
        >{{ item.text }}</span
      >
    </div>
  </div>
</template>

<script>
import { isLogin } from "@/utils/data";
import { useRouter } from "vue-router";

import homeActiveIcon from "../../assets/imagesnew/home_light.png";
import homeInactiveIcon from "../../assets/imagesnew/home_grey.png";
import repayActiveIcon from "../../assets/imagesnew/repayment_light.png";
import repayInactiveIcon from "../../assets/imagesnew/repayment_grey.png";
import mineActiveIcon from "../../assets/imagesnew/mine_light.png";
import mineInactiveIcon from "../../assets/imagesnew/mine_grey.png";

import { ref, watch } from "vue";

export default {
  setup() {
    const router = useRouter();

    const active = ref(0);

    const tabDataList = ref([
      {
        icon: {
          active: homeActiveIcon,
          inactive: homeInactiveIcon,
        },
        text: "首页",
        check: true,
      },
      {
        icon: {
          active: repayActiveIcon,
          inactive: repayInactiveIcon,
        },
        text: "还款",
        check: false,
      },
      {
        icon: {
          active: mineActiveIcon,
          inactive: mineInactiveIcon,
        },
        text: "我的",
        check: false,
      },
    ]);

    const onTabClick = (index) => {
      console.log("index---" + index);

      var dataList = tabDataList.value;

      dataList.forEach((element) => {
        element.check = false;
      });
      dataList[index].check = true;
      tabDataList.value = dataList;

      console.log("tabDataList----" + JSON.stringify(tabDataList.value));

      if ((index == 1 || index == 2) && !isLogin()) {
        router.push("/signin");
        return;
      } else {
        if (index == 0) {
          router.replace("/home");
        } else if (index == 1) {
          router.replace("/repayment");
        } else {
          router.replace("/mine");
        }
      }
    };

    const setTabData = (index) => {
      var dataList = tabDataList.value;
      dataList.forEach((element) => {
        element.check = false;
      });
      dataList[index].check = true;
      tabDataList.value = dataList;
    };

    return {
      tabDataList,
      router,
      active,
      onTabClick,
      setTabData,
    };
  },

  mounted() {
    var path = this.router.currentRoute.value.path;
    if (path) {
      if (path == "/home") {
        if (this.active == 0) {
          return;
        }
        this.setTabData(0);
      } else if (path == "/repayment") {
        if (this.active == 1) {
          return;
        }
        this.setTabData(1);
      } else {
        if (this.active == 2) {
          return;
        }
        this.setTabData(2);
      }
    }
  },
};
</script>

<style lang="less" scoped>
// @import "../../styles/common.less";
</style>
