<template>
  <div>
    <div class="nut-cell-container" :title="inputParam.paramName">
      <span class="title">{{ inputParam.paramName }}</span>

      <div class="nut-cell-item-box" v-if="inputParam.paramType == 1 || inputParam.paramType == 3">
        <nut-input v-model="inputParam.inputValue" @change="inputChange" class="nut-cell-input" input-align="right" :type="inputParam.number ? 'number' : 'text'" :placeholder="inputParam.inputDesc" style="background-color: transparent;" />
      </div>
      <div class="nut-cell-item-box" v-on:click="cellFocusAction" v-else-if="inputParam.paramType == 2 || inputParam.paramType == 7">
        <span class="selectHolder" v-show="!inputParam.inputValue">{{ inputParam.inputDesc }}</span>
        <span class="selectedValue" v-show="inputParam.inputValue">{{ selectValue }}</span>
        <img  class="selectImg" src="../../assets/imagesnew/arrow_right.png"/>
      </div>
      <div class="nut-cell-item-box" v-on:click="cellFocusAction" v-else-if="inputParam.paramType == 6">
        <span class="selectHolder" v-show="!inputParam.inputValue">{{ inputParam.inputDesc }}</span>
        <span class="selectedValue" v-show="inputParam.inputValue">{{ inputParam.inputValue }}</span>
        <img  class="selectImg" src="../../assets/imagesnew/arrow_right.png"/>
      </div>
    </div>
    <nut-popup v-model:visible="show" position="bottom">
      <nut-picker
        v-if="inputParam.paramType == 2 || inputParam.paramType == 7"
        :columns="inputParam.selectVo"
        :field-names="customFieldName"
        cancel-button-text="取消"
        confirm-button-text="确认"
        @confirm="confirmPicker"
        @cancel="cancelPicker"
      />

      <nut-date-picker
        v-if="inputParam.paramType == 6"
        v-model="currentDate"
        :min-date="minDate"
        :max-date="maxDate"
        cancel-button-text="取消"
        confirm-button-text="确认"
        @confirm="confirmDate"
        @cancel="cancelDate"
      />
    </nut-popup>

  </div>
</template>

<script>
import { ref, reactive } from 'vue'
export default {
  props: {
    inputObject: Object,
  },
  setup(props) {
    
    const inputParam = reactive({ ...props.inputObject });
    const show = ref(false);

    // 日期 初始化
    const currentDate = ref(['2000', '06', '15'])
    if(inputParam.paramType == 6 && inputParam.inputValue){
      currentDate.value = inputParam.inputValue.split('-');
    }

    // 选择 初始化
    const selectValue = ref('');
    if((inputParam.paramType == 2  || inputParam.paramType == 7) && inputParam.inputValue){
      inputParam.selectVo.map(item => {
        if(item.type == inputParam.inputValue){
          selectValue.value = item.name;
        }
      })
    }


    const customFieldName = {
      text: 'name',
      value: 'type',
    };

    const inputChange = (e) => {
      console.log('val---', e.target.value)
      inputParam.inputValue = e.target.value
    }

    const cellFocusAction = () => {
      if(inputParam.paramType == 2 || inputParam.paramType == 7 || inputParam.paramType == 6){
        show.value = true;
      }
    }

    const confirmDate = ({ selectedValues }) => {
      console.log('ttt---',selectedValues)
      currentDate.value = selectedValues;
      inputParam.inputValue = selectedValues.join('-');
      show.value = false;
    }

    const cancelDate = () => {
      show.value = false
    }


    const confirmPicker = ({ selectedValue, selectedOptions }) => {
      const item = selectedOptions[0];
      inputParam.inputValue = item.type;
      selectValue.value = item.name;
      show.value = false;
    }

    const cancelPicker = () => {
      show.value = false
    }

    return {
      inputParam,
      selectValue,
      show,
      currentDate,
      customFieldName,
      inputChange,
      cellFocusAction,
      confirmDate,
      cancelDate,
      confirmPicker,
      cancelPicker,
      minDate: new Date(1960, 0, 1),
      maxDate: new Date(2027, 5, 1),
    }
  }

}
</script>

<style scoped lang="less">

.nut-cell-container{
  background: #fff;
  border-radius: 7px;
  height: 54px;
  padding: 4px 8px 4px 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.nut-cell-item-box{
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;

}

.selectImg{
  width: 16px;
  height: 16px;
}

.nut-cell-input{
  text-align: right;
  padding: 10px;
  border: none;
  color: #000;
}
input::-webkit-input-placeholder{
    color:#999;
}


.selectedValue{
  color: #000;
}
.selectHolder{
  color: #999;
}

</style>