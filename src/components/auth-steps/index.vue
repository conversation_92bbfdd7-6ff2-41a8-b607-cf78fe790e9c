<template>
    <div style="width: 100%;height: 100%;">
        <span style="font-size: 14px;color: black;">贷款步骤</span>

        <div style="width: 100%;height: 10px;"></div>

        <div v-for="(item,index) in data" :key="index" style="width: 100%;" class="nut-flex-v">
            <div class="nut-flex-h-center">
                <img :src="item.finished?imgCheckYes:imgCheckNot" style="width: 17px;height: 17px;"alt="">
                <span style="font-size: 13px;color: #444444;margin-left: 20px;">{{index+1}}.{{item.content}}</span>
            </div>
            <div v-if="index!=data.length-1" style="width: 17px;" class="nut-flex-v-center">
                <div style="height: 25px;width: 1px;margin: 5px 0px;background-color: #D2D2D2;"></div>
            </div>
        </div>
    </div>
</template>
<script>

import { useRouter } from 'vue-router';
import { ref } from "vue";

import imgCheckNot from '../../assets/imagesnew/check_not_d2.png'
import imgCheckYes from '../../assets/imagesnew/check_yes.png'

export default {
    props: {
        data: Object,
    },

    setup(props) {
        const router = useRouter();

        return {
            imgCheckNot,
            imgCheckYes
        };
    },

    mounted() {

    },
}
</script>
<style lang="less" scoped>
// @import "../../styles/common.less";
</style>
