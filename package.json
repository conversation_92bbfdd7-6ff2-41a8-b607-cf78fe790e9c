{"name": "<PERSON><PERSON>", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build:xdh": "vite build --mode production-xdh", "build:test": "vite build --mode production-test", "build:yyt": "vite build --mode production-yyt", "build:jkh": "vite build --mode production-jkh", "build:wwqb": "vite build --mode production-wwqb", "build:ymt": "vite build --mode production-ymt", "build:ymh": "vite build --mode production-ymh", "build:ayqb": "vite build --mode production-ayqb"}, "dependencies": {"@nutui/nutui": "^4.3.13", "ali-oss": "^6.21.0", "axios": "^1.6.7", "crypto-js": "^4.2.0", "fs-extra": "^11.2.0", "heic2any": "^0.0.4", "jsencrypt": "^3.3.2", "jsencrypt-ext": "^2.1.2", "terser": "^5.29.1", "vconsole": "^3.15.1", "vue": "^3.2.45", "vue-i18n": "^9.10.1", "vue-loading-overlay": "^6.0.6", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.3.4", "less": "^4.1.3", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.3"}}